// imageLoadingService.ts - New robust image loading system
import apiClient from '@shared/helpers/apiClient.ts';
import { documentSlice } from '@shared/store/documentSlice.ts';
import { AppThunk } from '@shared/store/store.ts';
import { AxiosError } from 'axios';

// Types for image loading
export interface ImageLoadRequest {
  inboxId: string;
  docId: string;
  pageNo: number;
  type: 'thumbnail' | 'fullImage';
  priority: number;
  onSuccess?: (data: string) => void;
  onError?: (error: string) => void;
}

export interface ImageLoadResult {
  success: boolean;
  data?: string; // base64 image data
  error?: string;
}

export interface LoadingState {
  isLoading: boolean;
  hasError: boolean;
  retryCount: number;
  lastAttempt?: number;
}

// Configuration
const CONFIG = {
  THUMBNAIL_WIDTH: 490,
  THUMBNAIL_QUALITY: 30,
  FULL_IMAGE_WIDTH: 2000,
  MAX_RETRIES: 3,
  RETRY_DELAY_BASE: 1000, // 1 second base delay
  BATCH_SIZE: 8,
  REQUEST_TIMEOUT: 30000, // 30 seconds
  MAX_CONCURRENT_REQUESTS: 6,
  DEBUG_LOGGING: true, // Enable detailed logging
};

// Debug logging utility
const debugLog = (message: string, data?: any) => {
  if (CONFIG.DEBUG_LOGGING) {
    console.log(`[ImageLoader] ${message}`, data || '');
  }
};

// Error tracking
interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<string, number>;
  recentErrors: Array<{ timestamp: number; error: string; url: string }>;
}

const errorStats: ErrorStats = {
  totalErrors: 0,
  errorsByType: {},
  recentErrors: [],
};

const trackError = (error: string, url: string) => {
  errorStats.totalErrors++;
  errorStats.errorsByType[error] = (errorStats.errorsByType[error] || 0) + 1;
  errorStats.recentErrors.push({
    timestamp: Date.now(),
    error,
    url,
  });

  // Keep only last 50 errors
  if (errorStats.recentErrors.length > 50) {
    errorStats.recentErrors.shift();
  }

  debugLog('Error tracked', { error, url, stats: errorStats });
};

// Request management
class ImageLoadingManager {
  private requestQueue: ImageLoadRequest[] = [];
  private activeRequests = new Map<string, AbortController>();
  private loadingStates = new Map<string, LoadingState>();
  private imageCache = new Map<string, string>();
  private concurrentRequests = 0;

  // Generate unique key for request
  private getRequestKey(docId: string, pageNo: number, type: 'thumbnail' | 'fullImage'): string {
    return `${docId}-${pageNo}-${type}`;
  }

  // Convert blob to base64 with proper error handling
  private async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert blob to base64'));
        }
      };
      reader.onerror = () => reject(new Error('FileReader error'));
      reader.readAsDataURL(blob);
    });
  }

  // Build image URL
  private buildImageUrl(
    inboxId: string,
    docId: string,
    pageNo: number,
    type: 'thumbnail' | 'fullImage',
  ): string {
    const baseUrl = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}`;

    if (type === 'thumbnail') {
      return `${baseUrl}?w=${CONFIG.THUMBNAIL_WIDTH}&q=${CONFIG.THUMBNAIL_QUALITY}`;
    }
    return `${baseUrl}?w=${CONFIG.FULL_IMAGE_WIDTH}`;
  }

  // Load single image with retry logic
  private async loadSingleImage(request: ImageLoadRequest): Promise<ImageLoadResult> {
    const requestKey = this.getRequestKey(request.docId, request.pageNo, request.type);

    // Check cache first
    const cached = this.imageCache.get(requestKey);
    if (cached) {
      return { success: true, data: cached };
    }

    // Check if already loading
    if (this.activeRequests.has(requestKey)) {
      return { success: false, error: 'Already loading' };
    }

    const controller = new AbortController();
    this.activeRequests.set(requestKey, controller);

    const loadingState = this.loadingStates.get(requestKey) || {
      isLoading: false,
      hasError: false,
      retryCount: 0,
    };

    loadingState.isLoading = true;
    loadingState.lastAttempt = Date.now();
    this.loadingStates.set(requestKey, loadingState);

    try {
      const url = this.buildImageUrl(request.inboxId, request.docId, request.pageNo, request.type);

      const response = await apiClient.get(url, {
        signal: controller.signal,
        responseType: 'blob',
        timeout: CONFIG.REQUEST_TIMEOUT,
        headers: {
          accept: 'image/*',
        },
      });

      const base64Data = await this.blobToBase64(response.data);

      // Cache the result
      this.imageCache.set(requestKey, base64Data);

      // Update loading state
      loadingState.isLoading = false;
      loadingState.hasError = false;
      loadingState.retryCount = 0;
      this.loadingStates.set(requestKey, loadingState);

      // Call success callback if provided
      if (request.onSuccess) {
        request.onSuccess(base64Data);
      }

      return { success: true, data: base64Data };
    } catch (error) {
      loadingState.isLoading = false;
      loadingState.hasError = true;
      this.loadingStates.set(requestKey, loadingState);

      if (controller.signal.aborted) {
        return { success: false, error: 'Request aborted' };
      }

      const axiosError = error as AxiosError;
      const shouldRetry =
        loadingState.retryCount < CONFIG.MAX_RETRIES &&
        (axiosError?.response?.status === 503 ||
          axiosError?.response?.status === 500 ||
          axiosError?.code === 'ERR_NETWORK');

      if (shouldRetry) {
        loadingState.retryCount++;
        this.loadingStates.set(requestKey, loadingState);

        // Exponential backoff
        const delay = CONFIG.RETRY_DELAY_BASE * 2 ** (loadingState.retryCount - 1);

        setTimeout(() => {
          this.loadSingleImage(request);
        }, delay);

        return { success: false, error: `Retrying (${loadingState.retryCount}/${CONFIG.MAX_RETRIES})` };
      }

      // Call error callback if provided
      if (request.onError) {
        request.onError(axiosError?.message || 'Unknown error');
      }

      return { success: false, error: axiosError?.message || 'Unknown error' };
    } finally {
      this.activeRequests.delete(requestKey);
      this.concurrentRequests--;
    }
  }

  // Process request queue
  private async processQueue(): Promise<void> {
    while (this.requestQueue.length > 0 && this.concurrentRequests < CONFIG.MAX_CONCURRENT_REQUESTS) {
      const request = this.requestQueue.shift();
      if (!request) continue;

      this.concurrentRequests++;
      // Don't await here - let it run in parallel but track the promise
      this.loadSingleImage(request).catch((error) => {
        debugLog('Error in queue processing', { error, request });
      });
    }
  }

  // Public method to queue image load request
  public async queueImageLoad(request: ImageLoadRequest): Promise<void> {
    // Add to queue with priority sorting
    this.requestQueue.push(request);
    this.requestQueue.sort((a, b) => b.priority - a.priority);

    // Process queue
    this.processQueue();
  }

  // Cancel all requests for a document
  public cancelDocumentRequests(docId: string): void {
    // Cancel active requests
    for (const [key, controller] of this.activeRequests.entries()) {
      if (key.startsWith(docId)) {
        controller.abort();
        this.activeRequests.delete(key);
      }
    }

    // Remove from queue
    this.requestQueue = this.requestQueue.filter((req) => req.docId !== docId);

    // Clear loading states
    for (const key of this.loadingStates.keys()) {
      if (key.startsWith(docId)) {
        this.loadingStates.delete(key);
      }
    }
  }

  // Get loading state for a specific image
  public getLoadingState(docId: string, pageNo: number, type: 'thumbnail' | 'fullImage'): LoadingState {
    const key = this.getRequestKey(docId, pageNo, type);
    return (
      this.loadingStates.get(key) || {
        isLoading: false,
        hasError: false,
        retryCount: 0,
      }
    );
  }

  // Clear cache for memory management
  public clearCache(docId?: string): void {
    if (docId) {
      for (const key of this.imageCache.keys()) {
        if (key.startsWith(docId)) {
          this.imageCache.delete(key);
        }
      }
    } else {
      this.imageCache.clear();
    }
  }
}

// Global instance
const imageManager = new ImageLoadingManager();

// Redux thunks for image loading
export const loadDocumentThumbnails =
  (inboxId: string, docId: string, pageCount: number): AppThunk =>
  async (dispatch, getState) => {
    console.log(`🔥 SIMPLIFIED: Loading ALL thumbnails for document: ${docId} (${pageCount} pages)`);

    // Set loading state
    dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: true }));

    // Get current state to check what's already loaded
    const state = getState();
    const pageImagesMap = state.document.pageImagesMap;

    // Create array of pages that need thumbnails
    const pagesToLoad: number[] = [];
    for (let pageNo = 1; pageNo <= pageCount; pageNo++) {
      const hasThumb = pageImagesMap?.[docId]?.[pageNo]?.thumbUrl;
      if (!hasThumb) {
        pagesToLoad.push(pageNo);
      }
    }

    console.log(`🔥 Need to load thumbnails for ${pagesToLoad.length} pages:`, pagesToLoad);

    if (pagesToLoad.length === 0) {
      console.log('🔥 All thumbnails already loaded');
      dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: false }));
      return;
    }

    // Load ALL thumbnails in parallel - no queue, no limits, no complex logic
    const loadPromises = pagesToLoad.map(async (pageNo) => {
      try {
        console.log(`🔥 Starting load for page ${pageNo}`);
        const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=490&q=30`;

        const response = await apiClient.get(url, {
          responseType: 'blob',
          timeout: 30000,
          headers: { accept: 'image/*' },
        });

        // Convert to base64
        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(response.data);
        });

        console.log(`🔥 ✅ Thumbnail loaded for page ${pageNo}`);

        // Update Redux immediately
        dispatch(
          documentSlice.actions.setPageImage({
            docId,
            pageNo,
            thumbUrl: base64Data,
          }),
        );

        return { pageNo, success: true };
      } catch (error) {
        console.error(`🔥 ❌ Failed to load thumbnail for page ${pageNo}:`, error);
        return { pageNo, success: false, error };
      }
    });

    // Wait for ALL thumbnails to complete
    console.log(`🔥 Waiting for ${loadPromises.length} thumbnail requests to complete...`);
    const results = await Promise.allSettled(loadPromises);

    // Log results
    const successful = results.filter((r) => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;

    console.log(`🔥 Thumbnail loading completed: ${successful} successful, ${failed} failed`);

    // Mark loading as complete
    dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: false }));
  };

export const loadPageImage =
  (inboxId: string, docId: string, pageNo: number): AppThunk =>
  async (dispatch, getState) => {
    console.log(`🔥 SIMPLIFIED: Loading full image for page ${pageNo} of document ${docId}`);

    // Check if already loaded
    const state = getState();
    const pageImagesMap = state.document.pageImagesMap;
    if (pageImagesMap?.[docId]?.[pageNo]?.imageUrl) {
      console.log(`🔥 Full image for page ${pageNo} already loaded`);
      return;
    }

    // Set loading state
    dispatch(documentSlice.actions.setPageImageLoading({ docId, pageNo, isLoading: true }));

    try {
      console.log(`🔥 Starting full image load for page ${pageNo}`);
      const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=2000`;

      const response = await apiClient.get(url, {
        responseType: 'blob',
        timeout: 30000,
        headers: { accept: 'image/*' },
      });

      // Convert to base64
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(response.data);
      });

      console.log(`🔥 ✅ Full image loaded for page ${pageNo}`);

      // Update Redux immediately
      dispatch(
        documentSlice.actions.setPageImage({
          docId,
          pageNo,
          imageUrl: base64Data,
        }),
      );
    } catch (error) {
      console.error(`🔥 ❌ Failed to load full image for page ${pageNo}:`, error);
    } finally {
      // Always clear loading state
      dispatch(documentSlice.actions.setPageImageLoading({ docId, pageNo, isLoading: false }));
    }
  };

// Cleanup function for when documents are unloaded
export const cleanupDocumentImages =
  (docId: string): AppThunk =>
  async (dispatch) => {
    console.log(`Cleaning up images for document: ${docId}`);

    // Cancel any pending requests
    imageManager.cancelDocumentRequests(docId);

    // Clear cache for this document
    imageManager.clearCache(docId);

    // Clear Redux state
    dispatch(documentSlice.actions.clearDocumentImages(docId));
  };

// Thumbnail-first loading strategy
export const loadDocumentWithThumbnailFirst =
  (inboxId: string, docId: string, pageCount: number, currentPageNo: number = 1): AppThunk =>
  async (dispatch, getState) => {
    console.log(`Starting thumbnail-first loading for document: ${docId}`);

    // Step 1: Load thumbnails for all pages
    dispatch(loadDocumentThumbnails(inboxId, docId, pageCount));

    // Step 2: Load full image for current page with high priority
    dispatch(loadPageImage(inboxId, docId, currentPageNo));

    // Step 3: Preload full images for adjacent pages (lower priority)
    const adjacentPages = [currentPageNo - 1, currentPageNo + 1].filter(
      (pageNo) => pageNo >= 1 && pageNo <= pageCount,
    );

    for (const pageNo of adjacentPages) {
      // Add a small delay to let the current page load first
      setTimeout(() => {
        dispatch(loadPageImage(inboxId, docId, pageNo));
      }, 500);
    }
  };

// Smart page navigation with preloading
export const navigateToPage =
  (inboxId: string, docId: string, pageNo: number, totalPages: number): AppThunk =>
  async (dispatch, getState) => {
    console.log(`Navigating to page ${pageNo} of document ${docId}`);

    // Load the current page image immediately
    dispatch(loadPageImage(inboxId, docId, pageNo));

    // Preload adjacent pages
    const preloadPages = [pageNo - 2, pageNo - 1, pageNo + 1, pageNo + 2].filter(
      (p) => p >= 1 && p <= totalPages && p !== pageNo,
    );

    preloadPages.forEach((p, index) => {
      // Stagger the preloading to avoid overwhelming the system
      setTimeout(() => {
        dispatch(loadPageImage(inboxId, docId, p));
      }, index * 200);
    });
  };

// Simplified batch thumbnail loading
export const loadThumbnailsBatch =
  (inboxId: string, docId: string, pageNumbers: number[]): AppThunk =>
  async (dispatch, getState) => {
    console.log(`🔥 SIMPLIFIED: Loading thumbnail batch for pages: ${pageNumbers.join(', ')}`);

    const state = getState();
    const pageImagesMap = state.document.pageImagesMap;

    // Filter out pages that already have thumbnails
    const missingThumbnails = pageNumbers.filter((pageNo) => !pageImagesMap?.[docId]?.[pageNo]?.thumbUrl);

    if (missingThumbnails.length === 0) {
      console.log('🔥 All thumbnails in batch already loaded');
      return;
    }

    // Load thumbnails in parallel
    const loadPromises = missingThumbnails.map(async (pageNo) => {
      try {
        const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=490&q=30`;
        const response = await apiClient.get(url, {
          responseType: 'blob',
          timeout: 30000,
          headers: { accept: 'image/*' },
        });

        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(response.data);
        });

        dispatch(
          documentSlice.actions.setPageImage({
            docId,
            pageNo,
            thumbUrl: base64Data,
          }),
        );
      } catch (error) {
        console.error(`🔥 ❌ Failed to load thumbnail for page ${pageNo}:`, error);
      }
    });

    await Promise.allSettled(loadPromises);
  };

// Progressive image enhancement (thumbnail -> full image)
export const enhancePageImage =
  (inboxId: string, docId: string, pageNo: number): AppThunk =>
  async (dispatch, getState) => {
    const state = getState();
    const pageImages = state.document.pageImagesMap?.[docId]?.[pageNo];

    // If we already have a full image, no need to enhance
    if (pageImages?.imageUrl) {
      console.log(`Page ${pageNo} already has full image`);
      return;
    }

    // If we don't have a thumbnail, load it first
    if (!pageImages?.thumbUrl) {
      console.log(`Loading thumbnail first for page ${pageNo}`);
      await dispatch(loadThumbnailsBatch(inboxId, docId, [pageNo]));
    }

    // Now load the full image
    console.log(`Enhancing page ${pageNo} with full image`);
    dispatch(loadPageImage(inboxId, docId, pageNo));
  };

// Debug and monitoring utilities
export const getImageLoadingStats = () => {
  return {
    errorStats,
    cacheSize: imageManager['imageCache'].size,
    activeRequests: imageManager['activeRequests'].size,
    loadingStates: Object.keys(imageManager['loadingStates']).length,
    queueSize: imageManager['requestQueue'].length,
    concurrentRequests: imageManager['concurrentRequests'],
  };
};

export const clearImageLoadingStats = () => {
  errorStats.totalErrors = 0;
  errorStats.errorsByType = {};
  errorStats.recentErrors = [];
  debugLog('Image loading stats cleared');
};

// Health check function
export const performImageLoadingHealthCheck = async (inboxId: string, docId: string, pageNo: number) => {
  debugLog('Performing health check', { inboxId, docId, pageNo });

  const startTime = Date.now();
  const testRequest: ImageLoadRequest = {
    inboxId,
    docId,
    pageNo,
    type: 'thumbnail',
    priority: 999,
  };

  try {
    const result = await imageManager['loadSingleImage'](testRequest);
    const duration = Date.now() - startTime;

    debugLog('Health check completed', {
      success: result.success,
      duration,
      error: result.error,
    });

    return {
      success: result.success,
      duration,
      error: result.error,
      timestamp: Date.now(),
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    debugLog('Health check failed', { error, duration });

    return {
      success: false,
      duration,
      error: error?.toString() || 'Unknown error',
      timestamp: Date.now(),
    };
  }
};

export default imageManager;
