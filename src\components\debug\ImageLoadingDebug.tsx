// ImageLoadingDebug.tsx - Debug component for monitoring image loading system
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { 
  getImageLoadingStats, 
  clearImageLoadingStats,
  performImageLoadingHealthCheck 
} from '@shared/services/imageLoadingService';
import { 
  selectThumbnailsLoading, 
  selectPageImageLoading, 
  selectImageLoadingErrors,
  selectPageImagesMap 
} from '@shared/store/documentSlice';

interface ImageLoadingDebugProps {
  docId?: string;
  inboxId?: string;
  isVisible?: boolean;
}

const ImageLoadingDebug: React.FC<ImageLoadingDebugProps> = ({ 
  docId, 
  inboxId, 
  isVisible = false 
}) => {
  const [stats, setStats] = useState<any>(null);
  const [healthCheck, setHealthCheck] = useState<any>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Redux state
  const thumbnailsLoading = useSelector(selectThumbnailsLoading);
  const pageImageLoading = useSelector(selectPageImageLoading);
  const imageLoadingErrors = useSelector(selectImageLoadingErrors);
  const pageImagesMap = useSelector(selectPageImagesMap);

  // Refresh stats
  const refreshStats = () => {
    const currentStats = getImageLoadingStats();
    setStats(currentStats);
  };

  // Auto-refresh stats
  useEffect(() => {
    if (isVisible) {
      refreshStats();
      const interval = setInterval(refreshStats, 1000);
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isVisible]);

  // Perform health check
  const runHealthCheck = async () => {
    if (docId && inboxId) {
      const result = await performImageLoadingHealthCheck(inboxId, docId, 1);
      setHealthCheck(result);
    }
  };

  // Clear stats
  const handleClearStats = () => {
    clearImageLoadingStats();
    refreshStats();
  };

  if (!isVisible) {
    return null;
  }

  const containerStyle: React.CSSProperties = {
    position: 'fixed',
    top: 10,
    right: 10,
    width: 400,
    maxHeight: '80vh',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    color: 'white',
    padding: 16,
    borderRadius: 8,
    fontSize: 12,
    fontFamily: 'monospace',
    zIndex: 10000,
    overflow: 'auto',
  };

  const sectionStyle: React.CSSProperties = {
    marginBottom: 16,
    paddingBottom: 8,
    borderBottom: '1px solid #333',
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: '#0085ff',
    color: 'white',
    border: 'none',
    padding: '4px 8px',
    borderRadius: 4,
    fontSize: 10,
    cursor: 'pointer',
    marginRight: 8,
    marginBottom: 4,
  };

  return (
    <div style={containerStyle}>
      <div style={{ ...sectionStyle, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3 style={{ margin: 0, fontSize: 14 }}>Image Loading Debug</h3>
        <div>
          <button style={buttonStyle} onClick={refreshStats}>
            Refresh
          </button>
          <button style={buttonStyle} onClick={handleClearStats}>
            Clear Stats
          </button>
          {docId && inboxId && (
            <button style={buttonStyle} onClick={runHealthCheck}>
              Health Check
            </button>
          )}
        </div>
      </div>

      {/* System Stats */}
      <div style={sectionStyle}>
        <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>System Stats</h4>
        {stats && (
          <div>
            <div>Cache Size: {stats.cacheSize}</div>
            <div>Active Requests: {stats.activeRequests}</div>
            <div>Loading States: {stats.loadingStates}</div>
            <div>Queue Size: {stats.queueSize}</div>
            <div>Concurrent: {stats.concurrentRequests}</div>
            <div>Total Errors: {stats.errorStats.totalErrors}</div>
          </div>
        )}
      </div>

      {/* Redux State */}
      <div style={sectionStyle}>
        <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>Redux State</h4>
        <div>
          <div>Thumbnails Loading: {Object.keys(thumbnailsLoading).length}</div>
          <div>Page Images Loading: {Object.keys(pageImageLoading).length}</div>
          <div>Loading Errors: {Object.keys(imageLoadingErrors).length}</div>
          <div>Documents in Cache: {pageImagesMap ? Object.keys(pageImagesMap).length : 0}</div>
        </div>
      </div>

      {/* Current Document */}
      {docId && (
        <div style={sectionStyle}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>Current Document: {docId}</h4>
          <div>
            <div>Thumbnails Loading: {thumbnailsLoading[docId] ? 'Yes' : 'No'}</div>
            <div>
              Pages with Images: {
                pageImagesMap?.[docId] ? Object.keys(pageImagesMap[docId]).length : 0
              }
            </div>
            <div>
              Pages with Thumbnails: {
                pageImagesMap?.[docId] 
                  ? Object.values(pageImagesMap[docId]).filter(p => p.thumbUrl).length 
                  : 0
              }
            </div>
            <div>
              Pages with Full Images: {
                pageImagesMap?.[docId] 
                  ? Object.values(pageImagesMap[docId]).filter(p => p.imageUrl).length 
                  : 0
              }
            </div>
          </div>
        </div>
      )}

      {/* Error Details */}
      {stats?.errorStats.recentErrors.length > 0 && (
        <div style={sectionStyle}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>Recent Errors</h4>
          <div style={{ maxHeight: 150, overflow: 'auto' }}>
            {stats.errorStats.recentErrors.slice(-5).map((error: any, index: number) => (
              <div key={index} style={{ marginBottom: 4, fontSize: 10 }}>
                <div style={{ color: '#ff6b6b' }}>{error.error}</div>
                <div style={{ color: '#888' }}>
                  {new Date(error.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Health Check Results */}
      {healthCheck && (
        <div style={sectionStyle}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>Health Check</h4>
          <div>
            <div style={{ color: healthCheck.success ? '#51cf66' : '#ff6b6b' }}>
              Status: {healthCheck.success ? 'PASS' : 'FAIL'}
            </div>
            <div>Duration: {healthCheck.duration}ms</div>
            <div>Time: {new Date(healthCheck.timestamp).toLocaleTimeString()}</div>
            {healthCheck.error && (
              <div style={{ color: '#ff6b6b', fontSize: 10 }}>
                Error: {healthCheck.error}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Loading States Detail */}
      {Object.keys(pageImageLoading).length > 0 && (
        <div style={sectionStyle}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: 12 }}>Active Page Loading</h4>
          <div style={{ maxHeight: 100, overflow: 'auto' }}>
            {Object.entries(pageImageLoading).map(([key, isLoading]) => (
              <div key={key} style={{ fontSize: 10 }}>
                {key}: {isLoading ? 'Loading...' : 'Done'}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageLoadingDebug;
