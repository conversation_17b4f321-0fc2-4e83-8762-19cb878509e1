import AdminItemRow from '@components/admin/components/AdminItemRow.tsx';
import FormSection from '@components/admin/components/form/FormSection.tsx';
import ConfirmationDialog from '@components/shared/confirmation-dialog/ConfirmationDialog.tsx';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';
import { IClientDocType, docTypeClientToRaw } from '@shared/helpers/converters/doctype.ts';
import { extendedSearch, globalFuseOptions } from '@shared/helpers/helpers.ts';
import { useModal } from '@shared/hooks/useModal.tsx';
import { useNotification } from '@shared/hooks/useNotificationBar.tsx';
import { AdminQuickEditField } from '@shared/models/admin.ts';
import {
  adminSlice,
  adminValidTopologyTypesSelector,
  deleteDocTypes,
  patchDocType,
} from '@shared/store/adminSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import p from '@shared/styles/component/admin/admin-pages/admin-page.module.scss';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import clsx from 'clsx';
import Fuse from 'fuse.js';
import { cloneDeep } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router';

const fuseOptions = {
  ...globalFuseOptions,
  keys: ['name'],
};
const AdminInboxesDoctypes: React.FC = () => {
  const inboxDocTypes = useSelector((state) => state.admin.inboxDocTypes);
  const activeDocTypeList = useSelector((state) => state.admin.activeDocTypeList);
  const activeQuickEditField = useSelector((state) => state.admin.activeQuickEditField);
  const validTopologyTypes = useSelector(adminValidTopologyTypesSelector);
  const inboxMetadataTypes = useSelector((state) => state.admin.inboxMetadataTypes);

  const dispatch = useDispatch();
  const { showDialog } = useModal();
  const { t } = useTranslation();
  const { inboxId } = useParams();
  const { showNotification } = useNotification();

  const [fuseData, setFuseData] = useState([]);
  const [searchResults, setSearchResults] = useState<IClientDocType[]>(null);
  const [selectedDocTypes, setSelectedDocTypes] = useState<string[]>([]);

  const navigate = useNavigate();

  const fuse = new Fuse(fuseData, fuseOptions);

  const sortList = (list: IClientDocType[]) => {
    return list.sort((a, b) => {
      if (b.id === '@PB_NOTYPE') return -1;
      if (a.id === '@PB_NOTYPE') return 1;
      return a.name.localeCompare(b.name);
    });
  };
  const handleMatches = (input) => {
    const list = sortList(extendedSearch(input, fuse));
    setSearchResults(list);
  };

  const handleSearchInput = (value) => {
    if (value === '') {
      setSearchResults(sortList([...inboxDocTypes]));
    } else {
      handleMatches(value);
    }
  };

  const handleDelete = (setting: IClientDocType) => {
    showDialog(
      <ConfirmationDialog
        confirmAction={() => deleteDocTypes(inboxId, [setting.id])}
        text={t('admin:inboxes.sections.docTypeDelete')}
      />,
    );
  };

  const handleMultiDelete = () => {
    showDialog(
      <ConfirmationDialog
        confirmAction={() => {
          deleteDocTypes(inboxId, selectedDocTypes).then(() => setSelectedDocTypes([]));
        }}
        text={t('admin:inboxes.sections.docTypeMultiDelete')}
      />,
    );
  };
  useEffect(() => {
    return () => {
      dispatch(adminSlice.actions.setNewTempSubTypes(null));
    };
  }, [dispatch]);

  useEffect(() => {
    if (inboxDocTypes) {
      setSearchResults(sortList([...inboxDocTypes]));
      setFuseData(inboxDocTypes);
    }
  }, [inboxDocTypes]);

  useEffect(() => {
    setSelectedDocTypes(activeDocTypeList ?? []);
  }, [activeDocTypeList]);

  const [loadingRows, setLoadingRows] = useState<Record<string, boolean>>({});

  const handleEditField = (docTypeId: string, fieldName: string, newValue: any, valueKey?: string) => {
    setLoadingRows((prev) => ({ ...prev, [docTypeId]: true }));

    let data;

    if (valueKey) {
      const currentType = inboxDocTypes.find((item) => item.id === docTypeId);
      const list = cloneDeep(currentType[fieldName]);
      const item = list.findIndex((item) => item.id === valueKey);
      if (newValue === null) {
        list.splice(item, 1);
      } else {
        if (item === -1) {
          list.push({ id: valueKey, value: newValue });
        } else {
          list[item].value = newValue;
        }
      }

      data = { [fieldName]: list };
    } else {
      data = { [fieldName]: newValue };
    }

    const rawData = docTypeClientToRaw(data);

    return patchDocType(inboxId, docTypeId, rawData)
      .then(() => {
        // Reset loading state
        setLoadingRows((prev) => {
          const newState = { ...prev };
          delete newState[docTypeId];
          return newState;
        });
        showNotification(t('admin:notifications.updateSuccess'), 'success');
      })
      .catch(() => {
        setLoadingRows((prev) => {
          const newState = { ...prev };
          delete newState[docTypeId];
          return newState;
        });

        showNotification(t('admin:notifications.updateError'), 'error');
      });
  };

  const editFieldOptions = useMemo(() => {
    // Basic fields
    const basicFields: DropdownOption[] = [
      {
        label: t('admin:docType.name'),
        value: 'docTypeName',
        metadata: {
          label: t('admin:docType.name'),
          name: 'docTypeName',
          onChange: (id, val) => handleEditField(id, 'name', val),
          type: 'text',
          valueKey: 'name',
        } as AdminQuickEditField,
      },
      {
        label: t('admin:docType.providerId'),
        value: 'docTypeProviderId',
        metadata: {
          label: t('admin:docType.providerId'),
          name: 'docTypeProviderId',
          onChange: (id, val) => handleEditField(id, 'providerId', val),
          type: 'text',
          valueKey: 'providerId',
        } as AdminQuickEditField,
      },
    ];

    // Approval settings
    const approvalFields: DropdownOption[] = [
      {
        label: t('admin:docType.approvalThreshold'),
        value: 'docTypeApprovalThreshold',
        metadata: {
          label: t('admin:docType.approvalThreshold'),
          name: 'docTypeApprovalThreshold',
          onChange: (id, val) => handleEditField(id, 'approvalThreshold', val),
          type: 'text',
          valueKey: 'approvalThreshold',
        } as AdminQuickEditField,
      },
      {
        label: t('admin:docType.autoApprove'),
        value: 'docTypeAutoApprove',
        metadata: {
          label: t('admin:docType.autoApprove'),
          name: 'docTypeAutoApprove',
          onChange: (id, val) => handleEditField(id, 'autoApprove', val),
          type: 'boolean',
          valueKey: 'autoApprove',
        } as AdminQuickEditField,
      },
      {
        label: t('admin:docType.ocrThreshold'),
        value: 'docTypeOcrThreshold',
        metadata: {
          label: t('admin:docType.ocrThreshold'),
          name: 'docTypeOcrThreshold',
          onChange: (id, val) => handleEditField(id, 'ocrThreshold', val),
          type: 'text',
          valueKey: 'ocrThreshold',
        } as AdminQuickEditField,
      },
    ];

    // Doctype properties
    const doctypeFields: DropdownOption[] = [
      {
        label: t('admin:docType.ageThreshold'),
        value: 'docTypeAgeThreshold',
        metadata: {
          label: t('admin:docType.ageThreshold'),
          name: 'docTypeAgeThreshold',
          onChange: (id, val) => handleEditField(id, 'ageThreshold', Number.parseInt(val)),
          type: 'text',
          valueKey: 'ageThreshold',
        } as AdminQuickEditField,
      },
      {
        label: t('admin:docType.type'),
        value: 'docTypeTopologyType',
        metadata: {
          label: t('admin:docType.type'),
          name: 'docTypeTopologyType',
          onChange: (id, val) => handleEditField(id, 'topologyType', val),
          type: 'select',
          options: validTopologyTypes,
          valueKey: 'topologyType',
        } as AdminQuickEditField,
      },
    ];

    // Metadata fields from inboxMetadataTypes
    const metadataFields: DropdownOption[] = [];

    if (inboxMetadataTypes && inboxMetadataTypes.length > 0) {
      inboxMetadataTypes.forEach((metadataType) => {
        if (!metadataType.isArchived) {
          let options;
          if (metadataType.options && metadataType.options.length > 0) {
            options = metadataType.options.map((e) => {
              return { label: e.value, value: e.value };
            });
          }
          metadataFields.push({
            label: metadataType.name,
            value: `metadata_${metadataType.id}`,
            tag: { name: 'Metadata', value: 'metadata', isMinimal: true },
            color: '#0085FF',
            metadata: {
              label: metadataType.name,
              name: `metadata_${metadataType.id}`,
              onChange: (id, val) => {
                console.log(val);
                if (options) {
                  handleEditField(id, 'metadataKeys', val, metadataType.id);
                } else {
                  if (val === false) {
                    handleEditField(id, 'metadataKeys', null, metadataType.id);
                  } else {
                    handleEditField(id, 'metadataKeys', '', metadataType.id);
                  }
                }
              },

              type: options ? 'select' : 'boolean',
              options: options,
              valueList: 'metadataKeys',
              valueKey: metadataType.id,
              metadataId: metadataType.id,
            } as AdminQuickEditField,
          });
        }
      });
    }

    return [
      {
        options: basicFields,
      },
      {
        options: approvalFields,
      },
      {
        options: doctypeFields,
      },
      {
        label: t('admin:docType.quickEditCategories.metadata'),
        options: metadataFields,
      },
    ];
  }, [t, validTopologyTypes, inboxMetadataTypes]);

  const activeQuickEditFieldOption = useMemo(() => {
    // Find the option in the grouped structure
    for (const group of editFieldOptions) {
      if (group.options) {
        const option = group.options.find((e) => e.value === activeQuickEditField?.name);
        if (option) return option;
      }
    }
    return null;
  }, [activeQuickEditField, editFieldOptions]);

  useEffect(() => {
    return () => {
      dispatch(adminSlice.actions.setActiveQuickEditField(null));
    };
  }, []);

  return (
    <div className={clsx(s.form_body, s.form_body_scroll)}>
      <div className={p.body_header}>
        <h2>{t('admin:inboxes.docTypes')}</h2>
      </div>
      <p className={p.body_description}>{t('admin:inboxes.docTypesDescription')}</p>
      {validTopologyTypes.map((types) => {
        let list = searchResults?.filter((e) => e.topologyType === types.value) ?? [];
        if (types.value === 'bundle') {
          list =
            searchResults?.filter(
              (e) =>
                e.topologyType === 'bundle' || (e.topologyType !== 'document' && e.topologyType !== 'mail'),
            ) ?? [];
        }
        return (
          <FormSection
            key={types.value}
            noStyle
            scroll
            minHeight={Math.max(Math.min(list?.length * 50, 145), 58)}
            title={`${types.label} types`}
            editFieldSelect={{
              defaultValue: editFieldOptions[0][0],
              options: editFieldOptions,
              value: activeQuickEditFieldOption,
              onChange: (val) => {
                dispatch(adminSlice.actions.setActiveQuickEditField(val?.metadata));
              },
            }}
            add={{
              testId: `doctype-add-${types.label}`,
              onClick: () => navigate(`new?topologyType=${types.value}`),
              label: t('admin:inboxes.sections.addNew'),
            }}
            search={{
              onChange: (e) => handleSearchInput(e),
              placeholder: t('admin:inboxes.sections.docTypeSearch'),
            }}
            select={{
              handleEdit: () => {
                navigate(`multi?topologyType=${types.value}&ids=${selectedDocTypes.join(',')}`);
              },
              handleDelete: handleMultiDelete,
              setSelectedValues: (val) => {
                setSelectedDocTypes(val);
              },
              selectedValues: selectedDocTypes,
              allValues: [...(list ?? [])].map((e) => e.id).filter((e) => e !== '@PB_NOTYPE'),
            }}
            copy={{
              copyValues: list?.map((dt) => dt.id) ?? [],
            }}
          >
            <div className={clsx(s.row_list)}>
              {list?.map((setting, index) => {
                const isChecked = selectedDocTypes.findIndex((e) => e === setting.id) !== -1;
                return (
                  <AdminItemRow
                    animationSettings={{
                      enabled: true,
                      delay: 50 + index * 25,
                    }}
                    isChecked={isChecked}
                    setIsChecked={
                      setting.id !== '@PB_NOTYPE'
                        ? (val) => {
                            if (val) {
                              setSelectedDocTypes((cur) => {
                                return [...cur, setting.id];
                              });
                            } else {
                              setSelectedDocTypes((cur) => {
                                return [...cur].filter((e) => e !== setting.id);
                              });
                            }
                          }
                        : null
                    }
                    handleDelete={setting.id !== '@PB_NOTYPE' ? () => handleDelete(setting) : null}
                    handleNav={() => {
                      navigate(setting.id);
                    }}
                    handleCopy={() => {
                      navigator.clipboard.writeText(setting.id);
                      showNotification(t('home:notifications.copied'), 'success');
                    }}
                    key={`${setting.name}doctype${setting.id}`}
                    docType={setting}
                    title={setting.name}
                    isLoading={loadingRows[setting.id] || false}
                  />
                );
              })}
              {list?.length === 0 && (
                <div key={`${inboxId}doctype`} className={s.no_results}>
                  {t('admin:inboxes.sections.noDocTypeFound')}
                </div>
              )}
            </div>
          </FormSection>
        );
      })}
    </div>
  );
};

export default AdminInboxesDoctypes;
