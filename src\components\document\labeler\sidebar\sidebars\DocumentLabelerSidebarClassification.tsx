import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import DocumentSplitModal from '@components/document/split/DocumentSplitModal.tsx';
import DocumentTypeSwitchModalNew from '@components/document/type-selector/DocumentTypeSwitchModalNew.tsx';
import ConfirmationDialog from '@components/shared/confirmation-dialog/ConfirmationDialog.tsx';
import DownloadModal from '@components/shared/download-modal/DownloadModal.tsx';
import { IDocumentTopologyPartEnriched } from '@shared/helpers/converters/document.ts';
import { useGetActiveInboxQuery, useGetDoctypesQuery } from '@shared/helpers/rtk-query/firestoreApi';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { useModal } from '@shared/hooks/useModal.tsx';
import {
  selectActiveDocument,
  selectActivePageNo,
  selectIsMutation,
  selectMutations,
} from '@shared/store/documentSlice';
import { useSelector } from '@shared/store/store';
import { ReactComponent as BundleIcon } from '@svg/checks/bundle.svg';
import { ReactComponent as DocumentIcon } from '@svg/checks/document.svg';
import { ReactComponent as EmailIcon } from '@svg/checks/email.svg';
import { ReactComponent as ChevronDown } from '@svg/chevron-down.svg';
import { ReactComponent as DownloadIcon } from '@svg/download.svg';
import { ReactComponent as EditIcon } from '@svg/edit-icon.svg';
import { ReactComponent as ScissorsIcon } from '@svg/scissors.svg';
import { ReactComponent as TrashIcon } from '@svg/trash-icon-alt.svg';
import { ReactComponent as UndoIcon } from '@svg/undo.svg';
import clsx from 'clsx';
import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';

interface ClassificationAction {
  id: string;
  part?: IDocumentTopologyPartEnriched;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  onClick: () => void;
  tooltip?: string;
  highlighted?: boolean; // New property to control icon highlight/color
}

interface RowClassificationProps {
  LeftIcon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  badgeLabel: string;
  badgeStatus: string;
  value?: string;
  setActivePage?: () => void;
  actions?: ClassificationAction[];
  isActive?: boolean;
}

const RowClassification: React.FC<RowClassificationProps> = ({
  LeftIcon,
  badgeLabel,
  badgeStatus,
  value,
  setActivePage,
  actions,
  isActive,
}) => (
  <div
    className={clsx(s.row_classification, { [s.row_classification_active]: isActive })}
    data-status={badgeStatus}
  >
    <div
      className={clsx(s.row_item, { [s.row_item__inactive]: !setActivePage })}
      data-status={badgeStatus}
      onClick={() => setActivePage?.()}
    >
      <div className={s.row_item_left}>
        {LeftIcon && <LeftIcon className={s.icon} />}
        <div className={s.row_badge}>{badgeLabel}</div>
      </div>
      <div className={s.row_item_right} data-status={badgeStatus}>
        {value && (
          <span title={'test'} className={clsx(s.value, s.value__no_edit)}>
            {value}
          </span>
        )}
      </div>
    </div>
    {actions.length > 0 && (
      <div className={s.row_classification_actions}>
        {actions?.map((action) => {
          const isDelete = action.id.includes('delete');
          return (
            <div
              key={action.id}
              className={clsx(s.row_classification_action, {
                [s.delete]: isDelete,
                [s.highlighted]: action.highlighted,
              })}
              onClick={action.onClick}
              title={action.tooltip}
            >
              <action.icon />
            </div>
          );
        })}
      </div>
    )}
  </div>
);

const DocumentLabelerSidebarClassification: React.FC = () => {
  const { inboxId, docId } = useParams();
  const historical = location.pathname.includes('historical');
  const { t } = useTranslation();
  const { showModal, showDialog } = useModal();
  // Get state from Redux
  const activeDocument = useSelector(selectActiveDocument);
  const isMutation = useSelector(selectIsMutation);
  const mutations = useSelector(selectMutations);
  const activePageNo = useSelector(selectActivePageNo);

  // Helper function to get the check status for a specific element
  const getCheckStatus = useCallback(
    (elementId: string, elementType = 'classification') => {
      if (!activeDocument?.approvalChecks) return 'succeeded';

      let hasFailedCheck = false;
      let hasWarningCheck = false;

      // Check all approval checks for this element
      activeDocument.approvalChecks.forEach((check) => {
        // Check main target element
        if (
          check.details?.targetElement?.type === elementType &&
          check.type === 'user' &&
          check.details.targetElement.id === elementId
        ) {
          if (check.status === 'failed') {
            hasFailedCheck = true;
          } else if (check.status === 'warning') {
            hasWarningCheck = true;
          }
        }

        // Check items in the check
        if (check.details?.items && check.type === 'user') {
          check.details.items.forEach((item) => {
            if (item.targetElement?.type === elementType && item.targetElement.id === elementId) {
              if (item.status === 'failed') {
                hasFailedCheck = true;
              } else if (item.status === 'warning') {
                hasWarningCheck = true;
              }
            }
          });
        }
      });

      // Return the appropriate status
      let status = '';
      if (hasFailedCheck) status = 'failed';
      else if (hasWarningCheck) status = 'warning';
      return status;
    },
    [activeDocument],
  );

  // Get operations from the hook
  const {
    changeDocType,
    deleteTopologyPart,
    deleteDoc,
    resetTopology,
    patchDoc,
    setActivePage,
    getDocumentPDF,
  } = useDocumentOperations();
  const { settings: inboxSettings } = useGetActiveInboxQuery({ inboxId }).data ?? {};
  const { data: doctypeData } = useGetDoctypesQuery({ inboxId });
  const docTypes = doctypeData?.all;
  const filteredDocTypes = doctypeData?.filtered;
  const topology = useMemo(() => activeDocument?.topology ?? null, [activeDocument]);
  const pageImagesMap = useSelector((state) => state.documentList.pageImagesMap);
  const currentThumbs = pageImagesMap?.[docId];
  const [isOpen, setIsOpen] = useState(true);

  const getDocTypeDetails = useCallback(
    (docTypeId: string) => docTypes?.find((dt) => dt.id === docTypeId) ?? null,
    [docTypes],
  );

  // Create classification items based on approval checks
  const classificationItems = useMemo(() => {
    if (!activeDocument || !activeDocument.topology || !activeDocument.topology.parts) {
      return { children: [] };
    }

    const classificationChildren = [];
    const bundleStatus = getCheckStatus('bundle');

    classificationChildren.push({
      id: 'bundle',
      name: 'Bundle',
      icon: BundleIcon,
      status: bundleStatus,
    });

    // Process each part in the topology
    activeDocument.topology.parts.forEach((part) => {
      if (part.archived) return;

      const isNoType = part.docTypeId === '@PB_NOTYPE' || !part.docTypeId;

      // Get the part status directly using our helper function
      // If the part has no type, it's always failed regardless of checks
      const status = isNoType ? 'failed' : getCheckStatus(part.id);

      // Calculate page range
      const pages = part.pages.filter((e) => !e.archived) || [];
      const startPage = pages.length > 0 ? Math.min(...pages.map((p) => p.bundlePageNo)) : 0;
      const endPage = pages.length > 0 ? Math.max(...pages.map((p) => p.bundlePageNo)) : 0;
      const pageRangeDisplay = startPage === endPage ? `P${startPage}` : `P${startPage} - ${endPage}`;

      classificationChildren.push({
        id: part.id,
        icon: part.topologyType === 'document' ? DocumentIcon : EmailIcon,
        name: pageRangeDisplay,
        status: status,
      });
    });

    console.log('DEBUG: Final classification children:', classificationChildren);

    // Log the bundle status from the classification children
    console.log('DEBUG: Bundle status from classification children:', classificationChildren[0]?.status);

    return {
      children: classificationChildren,
    };
  }, [activeDocument, docTypes, getCheckStatus]);

  // Get the bundle status from classification items
  const bundleStatus = useMemo(() => classificationItems.children[0]?.status, [classificationItems]);

  const hasArchivedContent = useMemo(() => {
    if (!activeDocument) return false;
    return activeDocument.topology.parts.some(
      (part) => part.archived || part.pages.some((page) => page.archived),
    );
  }, [activeDocument]);

  const handleDownload = async (topologyId: string) => {
    const func = () =>
      getDocumentPDF(topologyId).then((res) => {
        if (res.status !== 200) return false;
        const a = document.createElement('a');
        const filename = res.headers['content-disposition']?.match(/filename=(.+)/)[1];
        a.href = window.URL.createObjectURL(res.data);
        a.download = filename;
        a.setAttribute('target', '_blank');
        a.setAttribute('id', 'temp_download_url');
        a.click();
        a.remove();
        return true;
      });
    if (activeDocument?.topology.contentReady) {
      func();
    } else {
      showModal(<DownloadModal handleDownload={func} activeDocument={activeDocument} />);
    }
  };

  // --- Bundle-specific actions ---
  const handleBundleDownload = useCallback(() => {
    handleDownload(null);
  }, [handleDownload]);

  const handleBundleEdit = useCallback(() => {
    const validTypes = docTypes?.filter((e) => e.topologyType === 'bundle' && !e.isArchived);
    showModal(
      <DocumentTypeSwitchModalNew
        description={t('document:typeSwitch.bundlePartDescription')}
        documentTypes={validTypes}
        suggestions={activeDocument?.alternativeClassificationResults ?? []}
        handleConfirm={(e) => changeDocType(e.docType.id, e.subType?.id)}
        currentDocType={activeDocument?.docTypeDetails}
      />,
    );
  }, [activeDocument, changeDocType, docTypes, showModal, t]);

  const handleBundleDelete = () => {
    let text = t('document:deletion.original');
    if (isMutation) {
      text = t('document:deletion.copy');
    } else if (mutations.length > 0) {
      text += `. \n ${t('document:deletion.extra')}`;
    }
    showDialog(
      <ConfirmationDialog
        confirmAction={deleteDoc}
        text={text}
        title={isMutation ? t('document:deletion.deleteCopy') : t('document:deletion.deleteOriginal')}
      />,
    );
  };

  const getBundleActions = useCallback((): ClassificationAction[] => {
    if (historical) return [];
    return [
      {
        id: 'downloadBundle',
        icon: DownloadIcon,
        onClick: handleBundleDownload,
        tooltip: 'Download Bundle',
      },
      {
        id: 'editBundle',
        icon: EditIcon,
        onClick: handleBundleEdit,
        tooltip: 'Edit Bundle',
      },
      {
        id: 'deleteBundle',
        icon: TrashIcon,
        onClick: handleBundleDelete,
        tooltip: 'Delete Bundle',
      },
    ];
  }, [historical, handleBundleDownload, handleBundleEdit, handleBundleDelete]);

  // --- Topology part actions (common for parts) ---
  const getActionsForRow = useCallback(
    (row: { id: string; [key: string]: any }): ClassificationAction[] => {
      const actions: ClassificationAction[] = [];
      if (historical) return actions;
      if (row.canDownload) {
        actions.push({
          id: 'download',
          icon: DownloadIcon,
          onClick: () => handleDownload(row.id),
          tooltip: 'Download',
          highlighted: row.highlightDownload,
        });
      }
      if (row.canCut) {
        actions.push({
          id: 'scissors',
          icon: ScissorsIcon,
          onClick: () => {
            showModal(
              <DocumentSplitModal
                patchDocument={patchDoc}
                activeDocument={activeDocument}
                currentThumbs={currentThumbs}
                part={row.part}
              />,
            );
          },
          highlighted: row.highlightCut,
          tooltip: 'Cut',
        });
      }
      if (row.canEdit) {
        actions.push({
          id: 'edit',
          icon: EditIcon,
          onClick: () => {
            const validTypes = docTypes?.filter(
              (e) =>
                e.topologyType ===
                  (row.part.topologyType !== undefined ? row.part.topologyType : 'document') && !e.isArchived,
            );
            showModal(
              <DocumentTypeSwitchModalNew
                description={t('document:typeSwitch.bundlePartDescription')}
                documentTypes={validTypes}
                suggestions={row.part.alternativeClassificationResults ?? []}
                handleConfirm={(e) => changeDocType(e.docType.id, e.subType?.id, row.part.id)}
                currentDocType={row.part.docTypeDetails}
                topologyType={row.part.topologyType}
              />,
            );
          },
          highlighted: row.highlightEdit,
          tooltip: 'Edit',
        });
      }
      if (row.canDelete) {
        actions.push({
          id: 'delete',
          icon: TrashIcon,
          onClick: () => deleteTopologyPart(row.part.id),
          tooltip: 'Delete',
          highlighted: row.highlightDelete,
        });
      }
      return actions;
    },
    [
      activeDocument,
      changeDocType,
      currentThumbs,
      deleteTopologyPart,
      docTypes,
      handleDownload,
      patchDoc,
      showModal,
      t,
    ],
  );

  // --- Generate actions ---
  const bundleRowActions = useMemo(() => getBundleActions(), [getBundleActions]);

  return (
    <div className={s.section_wrapper}>
      <div className={clsx(s.section, { [s.section__closed]: !isOpen })}>
        <div
          className={s.header}
          onClick={() => {
            if (historical) setIsOpen(!isOpen);
          }}
        >
          <h2>Classification</h2>
          {historical && <ChevronDown />}
        </div>

        <div className={s.section_content}>
          <RowClassification
            LeftIcon={BundleIcon}
            badgeLabel="Bundle"
            badgeStatus={bundleStatus}
            value={activeDocument?.docTypeDetails?.name}
            actions={bundleRowActions}
          />
          {hasArchivedContent && inboxSettings.documentTransform && !historical && (
            <button
              onClick={() =>
                showDialog(
                  <ConfirmationDialog
                    title={t('document:toolbar.transformations.undoTitle')}
                    dialogType={'confirmation'}
                    confirmAction={resetTopology}
                    cancelAction={() => {}}
                    text={t('document:toolbar.transformations.undoDescription')}
                  />,
                )
              }
              className={s.header_button}
            >
              <UndoIcon /> <span>Reset Transformations</span>
            </button>
          )}

          {topology?.parts.map((part) => {
            if (part.archived) return null;

            const statusItem = classificationItems.children.find((item) => item.id === part.id);
            if (!statusItem) return null;

            const docType = getDocTypeDetails(part.docTypeId);
            const validTypes = docTypes?.filter(
              (e) =>
                e.topologyType === (part.topologyType !== undefined ? part.topologyType : 'document') &&
                !e.isArchived,
            );

            const activePages = part.pages.filter((e) => !e.archived);
            const isActive = activePages.some((e) => e.bundlePageNo === activePageNo);

            let partStatus = statusItem.status;
            if (!part.docTypeId) {
              const typeFiltered = filteredDocTypes?.filter((e) => e.topologyType === part.topologyType);
              if (typeFiltered.length > 0) {
                partStatus = 'failed';
              } else {
                partStatus = 'succeeded';
              }
            }

            const partRowActions = getActionsForRow({
              id: part.id,
              part: part,
              canCut: part.transformable && inboxSettings.documentTransform && activePages.length > 1,
              canDownload: true,
              canDelete: topology.transformable,
              canEdit: validTypes.length > 0,
              highlightEdit: !part.docTypeId,
            });

            return (
              <RowClassification
                key={part.id}
                LeftIcon={statusItem.icon}
                badgeLabel={statusItem.name}
                badgeStatus={partStatus}
                value={docType?.name ?? 'No Type'}
                actions={partRowActions}
                isActive={isActive}
                setActivePage={() => setActivePage(activePages[0].bundlePageNo)}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DocumentLabelerSidebarClassification;
