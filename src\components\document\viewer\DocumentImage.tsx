// DocumentImage.tsx - New robust document image component with loading states and animations
import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectPageImage, selectIsPageImageLoading, selectPageImageError } from '@shared/store/documentSlice';
import s from '@components/document/labeler/viewer/labeler-view.module.scss';

interface DocumentImageProps {
  docId: string;
  pageNo: number;
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  showLoadingIndicator?: boolean;
  animationDuration?: number;
}

const DocumentImage: React.FC<DocumentImageProps> = ({
  docId,
  pageNo,
  className,
  style,
  onLoad,
  onError,
  showLoadingIndicator = true,
  animationDuration = 300,
}) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Get image data and loading states from Redux
  const pageImage = useSelector(selectPageImage(docId, pageNo));
  const isLoading = useSelector(selectIsPageImageLoading(docId, pageNo));
  const error = useSelector(selectPageImageError(docId, pageNo, 'fullImage'));

  // Determine which image to display (full image or thumbnail fallback)
  const displaySrc = pageImage.displayUrl;

  // Handle image source changes with smooth transitions
  useEffect(() => {
    if (displaySrc && displaySrc !== currentSrc) {
      console.log(`Image source changed for page ${pageNo}:`, {
        from: currentSrc ? 'existing' : 'none',
        to: pageImage.imageUrl ? 'full' : 'thumbnail',
      });

      // Reset error state when starting to load a new image
      setHasError(false);

      // If we're switching from thumbnail to full image, prepare for transition
      if (currentSrc && pageImage.imageUrl && currentSrc !== pageImage.imageUrl) {
        // Preload the new image
        const img = new Image();
        img.onload = () => {
          // Smooth transition to new image
          setIsImageLoaded(false);
          setTimeout(() => {
            setCurrentSrc(displaySrc);
          }, 50);
        };
        img.onerror = () => {
          console.error(`Failed to preload image for page ${pageNo}`);
          setHasError(true);
          onError?.();
        };
        img.src = pageImage.imageUrl;
      } else {
        // Direct update for first load or thumbnail
        setCurrentSrc(displaySrc);
        setIsImageLoaded(false);
      }
    }
  }, [displaySrc, currentSrc, pageNo, pageImage.imageUrl, onError]);

  // Handle image load events
  const handleImageLoad = () => {
    console.log(`Image loaded for page ${pageNo}`);
    setIsImageLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  const handleImageError = () => {
    console.error(`Image failed to load for page ${pageNo}`);
    setHasError(true);
    setIsImageLoaded(true); // Set to true to indicate loading is complete (even though it failed)
    onError?.();
  };

  // Animation styles
  const imageStyle: React.CSSProperties = {
    ...style,
    width: '100%',
    opacity: isImageLoaded ? 1 : 0,
    transition: `opacity ${animationDuration}ms ease-out, transform ${animationDuration}ms ease-out`,
  };

  const loadingOverlayStyle: React.CSSProperties = {
    opacity: isLoading || (!isImageLoaded && !hasError) ? 1 : 0,
    transition: `opacity ${animationDuration}ms ease-out`,
  };

  return (
    <div ref={containerRef} className={clsx(className)} style={{ position: 'relative', overflow: 'hidden' }}>
      {currentSrc && !hasError && (
        <img
          ref={imgRef}
          src={currentSrc + '-d'}
          alt={`Document page ${pageNo}`}
          style={imageStyle}
          onLoad={handleImageLoad}
          onError={handleImageError}
          draggable={false}
          loading="eager"
          data-hj-suppress
        />
      )}

      {/* Loading indicator */}
      {showLoadingIndicator && (
        <div style={loadingOverlayStyle} className={s.image_loading_overlay}>
          <Ring color="#0085ff" size={16} lineWeight={3} />
          <span>{isLoading ? 'Loading image...' : 'Preparing...'}</span>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className={s.image_error_message}>
          <div style={{ textAlign: 'center' }}>
            <div>⚠️</div>
            <div style={{ marginTop: 8 }}>Failed to load page {pageNo}</div>
            {error && <div style={{ marginTop: 4, fontSize: 12 }}>{error}</div>}
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentImage;
