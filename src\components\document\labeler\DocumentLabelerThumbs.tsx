import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import DocumentThumbnail from '@components/document/viewer/DocumentThumbnail.tsx';
import { sleep } from '@shared/helpers/helpers';
import { useGetDoctypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { UrlParams } from '@shared/models/generic';
import { selectActiveDocument, selectActivePageNo, selectPageImagesMap } from '@shared/store/documentSlice';
import labelerSlice from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import { ReactComponent as DocumentIcon } from '@svg/document-icon.svg';
import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router';
import useAsyncEffect from 'use-async-effect';
import s from './viewer/labeler-view.module.scss';

interface Props {}

const DocumentLabelerThumbs: React.FC<Props> = () => {
  const { docId, inboxId }: UrlParams = useParams();
  // Get state from Redux
  const activeDocument = useSelector(selectActiveDocument);
  const activePageNo = useSelector(selectActivePageNo);
  // Get operations from the hook
  const { setActivePage } = useDocumentOperations();
  const pageImagesMap = useSelector(selectPageImagesMap);

  const { all: docTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const isThumbsVisible = useSelector((state) => state.labeler.isThumbsVisible);

  const dispatch = useDispatch();
  const location = useLocation();
  const historical = location.pathname.includes('historical');

  useEffect(() => {
    if (activeRef.current) {
      const el = activeRef.current as HTMLImageElement;
      el.scrollIntoView({ inline: 'center', behavior: 'smooth', block: 'center' });
    }
  }, [activePageNo]);

  const currentThumbs = pageImagesMap?.[docId];

  const [delayedActive, setDelayedActive] = useState(false);

  useAsyncEffect(async () => {
    if (isThumbsVisible) {
      setDelayedActive(false);

      sleep(300).then(() => setDelayedActive(true));
    } else {
    }
  }, [isThumbsVisible]);

  const activeRef = useRef(null);

  const titleFontSize = (title: string) => {
    const textLength = title.length;
    const maxSize = 14;
    const minSize = 12;
    const shrinkStartLength = 12;
    const shrinkEndLength = 25;

    if (textLength <= shrinkStartLength) {
      return maxSize;
    }
    if (textLength > shrinkStartLength && textLength <= shrinkEndLength) {
      const sizeDecrementPerChar = (maxSize - minSize) / (shrinkEndLength - shrinkStartLength);
      return maxSize - (textLength - shrinkStartLength) * sizeDecrementPerChar;
    }
    return minSize;
  };

  return (
    <div data-tour={'thumbs'} className={clsx(s.page_thumbs)}>
      {activeDocument?.topology?.parts && currentThumbs ? (
        <>
          {activeDocument.topology.parts.map((group) => {
            const validTypes = docTypes?.filter(
              (e) =>
                e.topologyType === (group.topologyType !== undefined ? group.topologyType : 'document') &&
                !e.isArchived,
            );

            if (group.archived) return null;

            const active = group.pages
              .filter((e) => !e.archived)
              .find((p) => p.bundlePageNo === activePageNo);
            const doctype = group.docTypeDetails;
            const subType = group.docTypeDetails?.subTypeDetails;

            let isLowConfidence = false;
            let name = group.name;
            if (doctype) {
              name = doctype?.name;
              if (subType) name += ` - ${subType.name}`;
              isLowConfidence = group.confidence < doctype.approvalThreshold / 100 || !group.confidence;
            }
            return (
              <div className={s.group} key={group.id}>
                <div
                  className={clsx(
                    s.group_header,
                    { [s.visible]: isThumbsVisible },
                    { [s.low_confidence]: isLowConfidence },
                    {
                      [s.failed]:
                        group.docTypeId === '@PB_NOTYPE' ||
                        (group.docTypeId == null && validTypes?.length > 1),
                    },
                    { [s.active]: active && isThumbsVisible && !historical },
                  )}
                >
                  <div className={s.group_wrapper}>
                    <div
                      className={s.name}
                      onClick={() => {
                        const activePages = group.pages.filter((e) => !e.archived);
                        setActivePage(activePages[0].bundlePageNo);
                      }}
                    >
                      {!doctype && <DocumentIcon style={{ height: 12, marginBottom: 1, marginLeft: -16 }} />}
                      {name.length > 15 ? (
                        <Tooltip
                          tooltipStyle={{ maxWidth: 180, padding: 10, lineHeight: 1.2 }}
                          position={'top'}
                          content={name}
                        >
                          <span style={{ fontSize: titleFontSize(name) }}>{name}</span>
                        </Tooltip>
                      ) : (
                        <span style={{ fontSize: titleFontSize(name) }}>{name}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className={s.group_items}>
                  {group.pages.map((page, index) => {
                    if (page.archived) return null;
                    return (
                      <DocumentThumbnail
                        key={page.pageNo + activeDocument.id}
                        docId={docId}
                        pageNo={page.bundlePageNo}
                        isActive={activePageNo === page.bundlePageNo}
                        isVisible={delayedActive}
                        onClick={() => {
                          dispatch(labelerSlice.actions.setActiveEntityPair(null));
                          setActivePage(page.bundlePageNo);
                        }}
                        className={clsx(
                          { [s.active]: activePageNo === page.bundlePageNo },
                          { [s.visible]: delayedActive },
                        )}
                        animationDelay={index * 50}
                      />
                    );
                  })}
                </div>
              </div>
            );
          })}
        </>
      ) : (
        <div className={s.group_items}>
          {activeDocument?.dimensions.map((_, i) => {
            const pageNo = i + 1;
            return (
              <DocumentThumbnail
                key={pageNo + docId}
                docId={docId}
                pageNo={pageNo}
                isActive={activePageNo === pageNo}
                isVisible={delayedActive}
                onClick={() => {
                  dispatch(labelerSlice.actions.setActiveEntityPair(null));
                  setActivePage(pageNo);
                }}
                className={clsx({ [s.active]: activePageNo === pageNo }, { [s.visible]: delayedActive })}
                animationDelay={i * 50}
                showPageNumber={true}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DocumentLabelerThumbs;
