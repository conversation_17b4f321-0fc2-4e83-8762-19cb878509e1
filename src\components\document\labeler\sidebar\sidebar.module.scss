@import '@shared/styles/base';


.container {
  display: flex;
  width: auto;
  height: 100%;
  background: transparent;
  interpolate-size: allow-keywords;

}


.bar {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 100%;
  //border-right: 1px solid #EEEEEE;
  //box-shadow: 0 5px 14px -5px rgba(0, 0, 0, 0.1);
  background: transparent;

  &__narrow {
    width: 300px;
  }

}


.bar_content {
  width: 100%;
  border-radius: 5px;
  overflow: auto;
  /* Changed from hidden to auto to allow scrolling */
  padding: 10px 5px;
  gap: 10px;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}


/* replace lines 42 to 121 */
.nav {
  $nav-button-size: 40px;

  background-color: white;
  height: 100%;
  padding: 10px;
  border-radius: 5px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: default;

  .button_main {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    height: $nav-button-size;
    border-radius: 0 !important;
    background: $paperbox-blue;
    color: white;

    svg {
      height: 16px;
    }

    &:disabled {
      transition: all 0.2s ease-in-out;
      border-radius: 50px;

      color: rgba(0, 133, 255, 0.5);
      background: $paperbox-blue--fade-extra;
    }
  }

  .button_tags {
    height: $nav-button-size;
    width: 100%;

    button {
      height: 100%;
    }

    :global(#tag-dropdown-button) {
      border-radius: unset;
    }

    :global(#tag-dropdown) {
      width: 100%;
    }

    :global(#tag-dropdown-item) {
      height: 30px;
    }
  }

  .button_nav {
    width: $nav-button-size;
    min-width: $nav-button-size;
    transition: all 0.2s ease-in-out;
    border-radius: 5px;
    background: $paperbox-blue;

    &:focus:not(:disabled),
    &:hover:not(:disabled) {
      opacity: 0.8;
      cursor: pointer;
      // Hover styles
    }

    &:active:not(:disabled) {
      background: #f8f8f8;
    }

    &:disabled {
      transition: all 0.2s ease-in-out;
      //color: rgba(0,0,0,0.65);
      border-radius: 50px;
      border: 1px solid #EEEEEE;
      color: $paperbox-blue--fade;
      background: $paperbox-blue--fade-extra;
      //background: #f8f8f8;
    }

    svg {
      width: 50%;
      margin-top: 0;
      stroke-width: 2px;
    }

    &__left {
      margin-left: 1px;
      border-radius: 0 5px 5px 0 !important;

      svg {
        transform: rotateY(180deg);
      }
    }

    &__right {
      margin-right: 1px;
      border-radius: 5px 0 0 5px !important;
    }
  }
}


.header {
  padding: 13px 13px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  background: linear-gradient(40deg, #f8f8f8, #FFFFFF);

  svg {
    transition: transform 0.3s ease-in-out;
  }

  .chevron_up {
    transform: rotate(180deg);
  }

  h2 {
    font-size: 14px;
    font-weight: 600;
    font-family: $headings-font;
  }

}


.header_button {
  padding: 5px 13px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #FBFCFD;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
  height: 35px;

  span {
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  svg {
    transition: transform 0.3s ease-in-out;
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: $paperbox-blue--fade-extra;
    color: $paperbox-blue;

    svg {
      opacity: 1;
      transform: rotate(360deg);
    }
  }

}


.section_wrapper {
  overflow: hidden;
  border-radius: 10px;
  padding: 10px;
  background: #FFFFFF;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
  // min-height: fit-content;
  min-height: 60px;
}


.unlink_button {
  margin: 10px auto;
  padding: 8px 16px;
  width: 100%;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  //background-color: #f8f8f8;
  border: 1px solid $error-light;
  border-radius: 5px;
  color: $error;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: $error-lighter;
    border: 1px solid $error;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}


.section {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  height: 100%;
  background: white;
  transition: margin-top 0.3s ease-in-out, gap 0.3s ease-in-out, padding-top 0.3s ease-in-out;
  will-change: margin-top, gap, padding-top;

  &:not(.section__closed) +& {
    margin-top: 0px;
    border-top: 1px solid #EEEEEE;
  }

  .section_empty {
    height: 40px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
  }

  .section_content {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 8px;
    transition: height 0.3s ease-in-out, gap 0.3s ease-in-out, min-height 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;

    .no_items {
      padding: 10px;
      color: #aeb3bc;
      font-style: italic;
      text-align: center;
    }

    &.checks_content {
      min-height: 42px;
      /* Ensure at least one row item height when open */
    }

    &.fields_content {
      height: auto;
      max-height: none;
    }
  }

  &__closed {
    min-height: 0;
    gap: 0px;

    .header svg {
      transform: rotate(-180deg);
    }

    .section_content {
      height: 0;
      overflow: hidden;

      &.checks_content {
        min-height: 0 !important;
      }

    }

  }
}


.row_classification {
  border: 1px solid #EEEEEE;
  border-radius: 5px;

  .row_item {
    border: unset;

    .row_item_right {
      max-width: unset;
      flex: 1 1 auto;
    }

    .value {
      font-weight: 600 !important;

    }

    &[data-status="failed"] {
      border: 1px solid $error-light !important;

      ~ .row_classification_actions {
        border-top: 1px solid $error-light;
      }
    }

    &[data-status="warning"] {
      border: 1px solid $warning-light !important;

      ~ .row_classification_actions {
        border-top: 1px solid $warning-light;
      }
    }

  }

  &_active {
    border: 1px solid $paperbox-blue--fade;

    .row_item {
      background: $paperbox-blue--fade-extra;

      .row_item_left {
        color: $paperbox-blue;
      }

      .row_badge {
        color: white;
        background: $paperbox-blue;
      }

      .row_item_right {
        color: $paperbox-blue;

        .value {
          color: $paperbox-blue;
        }
      }

      /* Different active states based on data-status */
      &[data-status="failed"] {
        background: $error-lighter;

        .row_item_left {
          color: $error;
        }

        .row_badge {
          color: white !important;
          background: $error !important;
        }

        .row_item_right {
          color: $error;

          .value {
            color: $error;
          }
        }
      }

      &[data-status="warning"] {
        background: $warning-lighter;

        .row_item_left {
          color: $warning;
        }

        .row_badge {
          color: white !important;
          background: $warning !important;
        }

        .row_item_right {
          color: $warning;

          .value {
            color: $warning;
          }
        }
      }
    }

    /* Different border colors based on data-status */
    &[data-status="failed"] {
      border: 1px solid $error-light;
    }

    &[data-status="warning"] {
      border: 1px solid $warning-light;
    }
  }
}


.row_info {
  cursor: pointer;
}


.row_classification_actions {
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  border-top: 1px solid #EEEEEE;

  .row_classification_action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: $font-color-black;

    svg {
      opacity: 0.7;
      width: 16px;
      height: 16px;
    }

    &:not(:first-child) {
      border-left: 1px solid #EEEEEE;
    }

    &.highlighted {
      color: $error;

      svg {
        animation: pulse 1s infinite;

        opacity: 1;
      }
    }

    &:hover {
      color: $paperbox-blue;

      svg {
        opacity: 1;
      }

      &.delete,
      &.highlighted {
        color: $error;
      }

    }
  }

}


.textarea {

  width: 100%;
  height: 60px;
  padding: 12px 12px;
  border-radius: 5px;
  background: #FBFCFD;
  border: 1px solid #EEEEEE;
  font-size: 14px;
  font-weight: 400;
  color: $font-color-black;

  &:focus {
    outline: none;
    border: 1px solid $paperbox-blue--fade;
    background: $paperbox-blue--fade-extra;
  }

}


.row_item {
  position: relative;
  display: flex;
  overflow: hidden;
  align-items: center;
  min-height: 40px;
  flex-shrink: 0;
  padding: 8px 12px;
  cursor: pointer;
  border: 1px solid #EEEEEE;
  background: white;
  border-radius: 5px;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  /* Ensure full width */

  /* Styling for row_item based on data-status */
  &[data-status="failed"] {
    color: $error;
    background: $error-lighter;

    &:not(.row_group .row_item__field) {
      border: 1px solid $error-light;
    }

    .row_item_left {
      color: $error;

      svg {
        color: $error;
      }
    }

    .row_badge {
      color: $error !important;
      background: $error-light !important;
    }
  }

  &[data-status="warning"] {
    color: $warning;
    background: $warning-lighter;

    &:not(.row_group .row_item__field) {
      border: 1px solid $warning-light;
    }

    .row_item_left {
      color: $warning;

      svg {
        color: $warning;
      }
    }

    .row_badge {
      color: $warning !important;
      background: $warning-light !important;
    }
  }

  &__inactive {
    cursor: default;
  }

  &.optional {
    border: 1px dotted #EEEEEE;
    opacity: 0.6;
  }

  &__activeCheck {
    box-shadow: 0 1px 10px rgba(black, 0.1);
  }

  &__faded {
    .row_item_left {
      opacity: 0.5 !important;
    }
  }

  form {
    display: contents;
  }

  .row_item_left {
    display: flex;
    align-items: center;
    //flex: 0 1 50%;
    //flex: 0 1 50%;
    //TODO CHeck this flex
    gap: 10px;
    height: 100%;
    overflow: hidden;
    max-width: max-content;
    transition: width 0.3s ease-in-out;

    svg {
      width: 18px;
      height: 18px;
      flex-shrink: 0;
    }

    .row_badge {
      overflow: hidden;
      width: 100%;
    }

    span {
      text-wrap: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 13px;
      font-weight: 500;
      min-width: 0;
      flex: 1;
    }
  }

  .row_item_right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    flex: 1 1 auto;
    gap: 10px;
    min-width: 0;

    &[data-status="failed"] {
      color: $error;

      .value {
        color: $error;
      }
    }

    &[data-status="warning"] {
      color: $warning;

      .value {
        color: $warning;
      }
    }

    .value {
      font-size: 14px;
      font-weight: 400;
      display: block;
      overflow: hidden;
      width: auto;
      min-width: 0;
      max-width: calc(110% - 32px);
      min-height: 14px;
      text-align: right;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 2px;

      &:hover:not(.value__no_edit) {
        text-decoration: underline;
        text-effect: outline;
      }

      &__pulsing {
        animation: pulseFade 1s ease-in-out infinite;
      }
    }
    .value_list{
      align-items: center;
      justify-content: flex-end;
      gap: 6px;
      font-size: 14px;
      display: flex;
      width: 100%;

      .value_text{
        border-radius: 5px;
        padding: 2px 4px;
        font-weight: 500;
        background: white;
      }
    }

    .image {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 45%;
      height: 28px;
      border: 1px solid #EEEEEE;
      border-radius: 5px;
      background: #FBFCFD;
      object-fit: cover;

      &__small {
        width: 60px;
        height: 20px;
      }
    }

    .actions {
      display: flex;
      overflow: hidden;
      flex-direction: row;
      flex-shrink: 0;
      max-width: 0;
      height: 18px;
      transition: max-width 0.15s ease-in-out;
      gap: 2px;

      button {
        width: 18px;
        height: 18px;

        svg {
          width: 18px;
          height: 18px;
          transition: transform 0.1s ease-in-out;
        }

        &:hover {
          svg {
            transform: scale(1.2);

          }
        }
      }

    }

    .icon {
      width: 18px;
      height: 18px;

      &.failed {
        color: $error;
      }

      &.warning {
        color: $warning;
      }
    }

    .target_icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      color: inherit;
    }

    .info {
      color: #EEEEEE;
    }

  }

  .checkmark {
    color: $success;
  }

  .warning {
    color: $warning;
  }

  .failed {
    color: $error;
  }

  .input {
    font-size: 14px;
    font-weight: 400;
    flex-grow: 1;
    height: calc(100% + 20px);
    margin-right: -2px;
    padding: 5px 32px 5px 12px;
    border: none;
    border-left: 1px solid $paperbox-blue--fade;

    &:focus {
      outline: none;
      box-shadow: none;
    }
  }

  .input_date {
    width: 200px;
    height: 40px;
    margin-right: -3px;
    margin-top: -10px;
    margin-bottom: -10px;

    &:focus {
      outline: none;
    }

    div[role='combobox'] {
      border: none !important;
      box-shadow: none !important;
      border-left: 1px solid #EEEEEE !important;
    }

    & > div {
      height: 100%;
      border-radius: 0;
    }

    :global(.rs-icon) {
      display: none;
      pointer-events: none;
    }

    :global(.rs-picker-toggle-read-only) {
      opacity: 1;
    }
  }

  .input_actions {
    position: absolute;
    right: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;

    .input_action {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      z-index: 1000;
      transition: transform 0.1s ease-in-out;

      &:hover {
        transform: scale(1.2);
      }

      svg {
        width: 18px;
        height: 18px;
        min-width: 18px;
      }

      .input_cancel {
        color: $error;
      }

      .input_confirm {
        color: $paperbox-blue;
      }
    }

  }

  &__field {
    padding: 8px 2px 8px 12px;

    &:hover,
    &.active {
      border: 1px solid $paperbox-blue--fade;
      background: $paperbox-blue--fade-extra;

      .actions {
        max-width: 25px;
        transition-delay: 0.05s;
      }

      &[data-status="failed"] {
        color: $error;
        background: $error-lighter;

        &:not(.row_group .row_item__field) {
          border: 1px solid $error-light;
        }
      }

      &[data-status="warning"] {
        color: $warning;
        background: $warning-lighter;

        &:not(.row_group .row_item__field) {
          border: 1px solid $warning-light;
        }
      }
    }

    /* Status styling is now handled by data-status attributes */

    &.pending {
      cursor: not-allowed;
      pointer-events: none;

      opacity: 0.5;

      .row_badge {
        animation: pulse 0.75s ease-in-out infinite;

      }
    }

    &.active {
      .value {
        font-weight: 500;
      }

      .row_badge {
        color: white;
        background: $paperbox-blue;
      }

    }
  }
}


.row_group {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  flex-shrink: 0;

  .row_item {
    border: none;
    border-radius: 0;
  }

  .row_group_children {
    display: flex;
    flex-direction: column;
    height: 0;
    transition: height 0.3s ease-in-out;
    overflow-y: hidden;

    .row_item {
      border-top: 1px dashed #EEEEEE;
    }
  }

  &__faded {
    .row_item_left {
      opacity: 0.5;
    }
  }

  &__active {
    border: 1px solid $paperbox-blue--fade;

    &[data-status="failed"] {
      border: 1px dotted $error;
    }

  }

  &__activeCheck {
    box-shadow: 0 1px 10px rgba(black, 0.1);
  }

  &__open {
    .row_group_children {
      animation: openAnimOverflow 0.3s ease-in-out forwards;
      height: auto;
    }
  }
}


@keyframes openAnimOverflow {
  0% {
    overflow-y: hidden;
  }

  99% {
    overflow-y: hidden;
  }

  100% {
    overflow-y: auto;
  }
}


.row_badge {
  font-size: 13px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3px 6px;
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
  color: $font-color-black;
  border-radius: 5px;
  background: #f8f8f8;
  gap: 3px;

  span {
    margin-top: 2px;
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
  }

  &[data-status="succeeded"] {
    color: $success !important;
    background: $success-light !important;
    border: none !important;
  }

  &[data-status="failed"] {
    color: $error !important;
    background: $error-light !important;
    border: none !important;
  }

  &[data-status="warning"] {
    color: $warning !important;
    background: $warning-light !important;
    border: none !important;
  }

}


.tabs {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
  min-height: 36px;
  max-height: 108px;
  /* Allows for up to 3 rows of tabs */
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  overflow-y: auto;
  background: #EEEEEE;
  gap: 1px;

  .tab {
    flex: 1 1 auto;
    min-width: 80px;
    max-width: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0 8px;
    font-size: 14px;
    font-weight: 500;
    color: rgba($font-color-black, 0.6);
    cursor: pointer;
    border-radius: 0;
    transition: all 0.2s ease;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: #FBFCFD;

    &.active {
      opacity: 1;
      font-weight: 700;
      background: white;
      z-index: 1;
    }

    &:hover:not(.active) {
      background: rgba(255, 255, 255, 0.5);
    }

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      display: block;
    }
  }
}


//Pulsing animation
@keyframes pulseFade {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.03);
  }

  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}


@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.03);
  }

  100% {
    transform: scale(1);
  }
}
