@import './src/shared/styles/base';


.container {
  position: relative;
  display: flex;
  overflow: auto;
  width: 100%;
  height: 100%;
  padding: 20px;
  transition: width 0.3s ease;

  &:focus {
    outline: none;
  }

  &__thin {
    width: calc(100% - 150px);
  }

}


.wrapper {
  position: relative;
  display: flex;
  overflow: auto;
  flex-direction: row;
  width: 100%;
  height: 100%;
  //scrollbar-gutter: stable;

  ::-webkit-scrollbar {
    height: 1px;
  }
}


.page_thumbs_wrapper {
  position: relative;
  height: 100%;

  .page_thumbs_filler {
    width: 0;
    height: 100%;
    transition: width 0.1s ease-in-out;
    will-change: width;
  }

  &__visible {
    .page_thumbs_filler {
      width: 160px;

    }
  }
}


.page_thumbs {
  display: flex;
  overflow-x: hidden;
  flex: 1;
  flex-direction: column;
  width: auto;
  height: 100%;
  padding: 0px 10px;
  gap: 10px;
  will-change: width;

  .group {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: auto;
    border-radius: 5px;

    &_items {
      display: flex;
      align-items: center;
      flex-direction: column;
      flex-shrink: 0;
      width: auto;
      margin-top: 10px;
      margin-bottom: 10px;
      gap: 10px;
    }

    &_header {
      font-size: 14px;
      font-weight: 600;
      position: sticky;
      z-index: 1;
      top: 0;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      min-width: 0;
      min-height: 45px;
      padding-top: 10px;
      padding-bottom: 0;
      transition: opacity 0.4s ease, transform 0.5s ease;
      transition-delay: 0s;
      transition-duration: 0s;
      transform: scale(0);
      transform-origin: center;
      opacity: 0;
      color: white;
      border-radius: 5px;

      .group_wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        border-radius: 5px;
        box-shadow: $shadow-light !important;
      }

      .info {
        z-index: 1;
        display: none;
        overflow: hidden;
        align-items: center;
        flex-shrink: 1;
        justify-content: space-between;
        width: 100%;
        min-width: 0;
        margin-top: -5px;
        padding-top: 5px;
        transform-origin: top center;
        color: black;
        border: 1px solid #EEEEEE;
        border-radius: 5px;
        background: white;
        gap: 4px;

        .buttons {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 100%;
          //gap: 4px;

        }

        button {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 36px;
          color: black;
          //border-radius: 3px;
          background: white;

          &:hover {
            color: $paperbox-blue;
          }

          &.delete {
            &:hover {
              color: $error;
            }
          }

          &:disabled {
            color: rgba(0, 0, 0, 0.3);
          }

          svg {
            width: 14px;
            height: auto;
          }

          &:not(:last-child) {
            border-right: 1px solid #EEEEEE;
          }

        }
      }

      .name {
        z-index: 3;
        display: flex;
        overflow: hidden;
        align-items: center;
        flex-shrink: 0;
        justify-content: center;
        width: 100%;
        height: auto;
        min-height: 30px;
        max-height: 50px;
        padding: 5px;
        cursor: pointer;
        text-align: center;
        text-overflow: ellipsis;
        border-radius: 5px;
        background: $dark-blue;
        gap: 2px;

        span {
          overflow: hidden;
          max-width: 70%;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

      }

      &.visible {
        transition-delay: 0.3s;
        transition-duration: 0.3s;
        transform: scale(1);
        opacity: 1;
      }



      &.active {

        .name {
          background: $paperbox-blue;
          border: 1px solid $paperbox-blue--fade;
          color: white;
        }
      }

      &.low_confidence {
        .name {
          border: none;
          background: $warning-lighter;
          color: $warning;
        }

        &.active {
          .name {
            background: $warning;
            color: white;
          }
        }
      }

      &.failed {
        .name {
          border: none;

          background: $error-lighter;
          color: $error;
        }

        &.active {
          .name {
            background: $error;
            color: white;
          }
        }

      }
    }

  }

  img {
    width: calc(100% - 2px);
    height: auto;
    // margin: 0 5px;
    cursor: pointer;
    transition: opacity 0.2s, transform 0s;
    // transform: scale(0);
    opacity: 1;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
    will-change: transform;
    image-rendering: optimizeSpeed;

    &.active,
    &:hover {
      opacity: 1;
      outline: 1px solid $paperbox-blue;
      box-shadow: 0px 0px 24px rgba(0, 102, 255, 0.25);
    }

    &.visible {
      transition: transform 0.3s ease !important;
      transform: scale(1);
      transform-origin: center;
    }

  }
}


.page_thumbs_handle {
  position: absolute;
  z-index: 100;
  top: 50%;
  left: -10px;
  cursor: pointer;
  background: red;
}

.loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  animation: fadeIn 0.1s forwards;
  animation-delay: 0.5s;
  opacity: 0;

}

.error_loading_container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  //animation: fadeIn 0.3s forwards 0.5s;
  //opacity: 0;
}

.error_loading {
  text-align: center;
  background-color: $error-lighter;
  border: 1px solid $error;
  color:$error;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 250px;
}


@keyframes fadeIn {
  from {
    opacity: 0;

  }

  to {
    opacity: 1;

  }
}


.inner_container {
  position: relative;
  display: flex;
  overflow: auto;
  overflow-x: hidden;
  flex: auto;
  min-width: 70%;
  max-width: 70%;
  margin: auto;
  cursor: all-scroll;
  box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
  will-change: min-width, max-width;

  &__reset {
    animation: resetRotation 0.3s;
  }
}


@keyframes resetRotation {

  0% {
    transform: rotate(-90deg);
  }

  100% {
    transform: rotate(0deg);
  }
}


.image {
  z-index: 0;
  width: 100%;
  height: 100%;
  user-select: none;
  object-fit: contain;
  image-rendering: -webkit-optimize-contrast;
  // Prevent layout shifts during loading
  max-width: 100%;
  max-height: 100%;
}

.image_loading_overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  animation: fadeIn 0.2s forwards;
  animation-delay: 0.3s;
  opacity: 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  pointer-events: none;
  font-size: 12px;
  color: #666;
  gap: 8px;
}

.image_error {
  z-index: 0;
  width: 100%;
  height: 100%;
  user-select: none;
  object-fit: contain;
  opacity: 0.3;
  filter: grayscale(100%);
}

.image_error_message {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  transform: translate(-50%, -50%);
  color: #666;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}


.selection {
  position: absolute;
  z-index: 10;
  user-select: none;

  pointer-events: none;
  border: 2px solid $warning;
  border-radius: 3px;
  background: rgba($warning, 0.1);
  will-change: top, left, width, height;
}


.table_hover {
  position: absolute;
  cursor: pointer;
  transition: opacity 0.15s;
  //opacity: 0;
  border-radius: 5px;
  background: #0085FF;

  &__hovered {
    opacity: 0.2;
  }

  &_button {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.15s;
    opacity: 0;
    color: white;
    border-radius: 5px;
    background: $paperbox-blue;

    &__hovered {
      opacity: 1;
    }

    svg {
      width: 14px;
    }
  }
}


.table_line_wrapper {
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;

  &__row {}
}


.table_line {

  background: none;

  &__col {
    width: 0;
    height: 100%;
    border-right: 2px dotted $warning;
  }

  &__row {
    width: 100%;
    height: 0;
    border-bottom: 2px dotted $warning;
  }

  &__delete {
    border-style: solid !important;
    border-color: $error !important;
  }

  &__temp {
    border-style: solid;
  }

  &__small {
    border-style: solid;
    border-color: white;

  }
}


.table_resize_wrapper {
  position: absolute;
  z-index: 12;

  &__corner {
    z-index: 13;
    width: 20px;
    height: 20px;
  }

}


.table_button {
  position: absolute;
  z-index: 11;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease-in-out;
  pointer-events: none;
  color: white;
  border-radius: 20px;

  &__delete {
    transform: rotate(45deg);
  }
}


.table_ruler_wrapper {
  position: absolute;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

}


.table_ruler {
  transition: opacity 0.2s ease-in-out;
  opacity: 0.2;
  border-radius: 5px;

  &:hover,
  &__active {
    opacity: 1;
  }

}


.table_cell {
  position: absolute;
  border-radius: 4px;
  background-color: rgba($paperbox-blue, 0.3);
}


.annotation {
  position: absolute;
  z-index: 9;
  box-sizing: content-box;

  padding: 0;
  cursor: pointer;
  transition: margin 0.2s, padding 0.2s, transform 0.2s, opacity 0.2s;
  border: 2px solid rgba(112, 221, 186, 1);
  border-radius: 3px;
  background: rgba(112, 221, 186, 0.2);

  &:hover:not(&__active):not(&__temp):not(&__table) {

    padding: 2px;
    transform: translateX(-2px) translateY(-2px);
  }

  &__table {
    background-color: unset;
  }

  &__active {
    padding: 3px;
    transform: translateX(-3px) translateY(-3px);
    border: 2px solid rgb(255, 14, 252);
    background: rgba(255, 14, 252, 0.2);
  }

  &__temp {
    transition: background-color 0.1s ease-in-out;
    transition-property: background-color, border-color;
    border-color: $warning;
    background: rgba($warning, 0.1);

    &:hover {
      border-color: $error;
      background: rgba($error, 0.1);
    }
  }

  &__source {
    &_user {
      border: 2px dashed #e7ff00;
      background: rgba(#e7ff00, 0.1);
    }

    &_model {

      border: 2px dashed #ff00ea;
      background: rgba(#ff00ea, 0.1);
    }
  }

  &__suggestion {
    border: 2px dashed #ff6a00;
    background: rgba(#ff6a00, 0.1);
  }

  &__faded {
    opacity: 0.2;
  }
}


.token {
  position: absolute;
  z-index: 8;
  width: 100%;
  margin: 0;
  cursor: pointer;
  user-select: none;
  opacity: 0;
  border-radius: 3px;
  background: rgba($warning, 0.1);
  will-change: opacity;

  &__default {
    opacity: 0;
    border: 1px solid $warning;
  }

  &__contained {
    opacity: 1;
  }

  &__uri {
    opacity: 1;
    border-bottom: 2px dotted $paperbox-blue;
    background: rgba($paperbox-blue, 0.1);

  }

  &:hover {
    opacity: 1;
  }

  &__searched {
    opacity: 1;
  }

  &__matched {
    opacity: 1;
    border: 2px solid $warning;
    border-radius: 1px;
    background: rgba(255, 126, 22, 0.4);
  }
}


.cursor_layer {
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
}


.annotation_layer {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
}
