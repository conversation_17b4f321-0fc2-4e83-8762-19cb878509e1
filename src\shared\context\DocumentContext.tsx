import {
  IDocument,
  IDocumentEnriched,
  IDocumentEntity,
  IDocumentEntityComplexValue,
  IDocumentTopologyPartEnriched,
  convertPartToRaw,
  entityToRawPayload,
} from '@shared/helpers/converters/document';
import { IClientMasterdataSearchResult } from '@shared/helpers/converters/masterdata-result.ts';
import { normalizeEntity, usePrevious, uuid4hex } from '@shared/helpers/helpers.ts';
import { enrichDocumentWithTypes, sortEntities } from '@shared/helpers/newHelpers.ts';
import { useGetHistoricalSiblingsQuery } from '@shared/helpers/rtk-query/analyticsApi.ts';
import { useGetDocumentJSONQuery } from '@shared/helpers/rtk-query/backendApi.ts';
import {
  firestoreApi,
  useGetDoctypesQuery,
  useGetDocumentMutationsQuery,
  useGetDocumentQuery,
  useGetFieldtypesQuery,
  useGetNextSiblingQuery,
  useGetPrevSiblingQuery,
  useGetTagtypesQuery,
} from '@shared/helpers/rtk-query/firestoreApi';
import useConsole from '@shared/hooks/useConsole.tsx';
import {
  IDocumentEntityPatchPayload,
  IDocumentEntityPostPayload,
  addEntityToDocument,
  bounceDocument,
  changeDocumentDoctype,
  changeDocumentInbox,
  createDocumentMutation,
  deleteDocument,
  deleteMutation,
  editEntityInDocument,
  getRawPDF,
  importMasterDataResult,
  lockDocument,
  patchDocument,
  patchTopologyPart,
  removeEntityFromDocument,
  unlockDocument,
  clearHistoricalDocumentLock,
} from '@shared/services/documentService.ts';
import { setIsThumbsVisible } from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import { AxiosResponse } from 'axios';
import { cloneDeep } from 'lodash';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router';
import { ActiveEntityPair, PageTokens } from '../models/document';
import { addPendingOperation, removePendingOperation } from '../store/pendingOperationsSlice';

type DocumentContextType = {
  mainDocument?: IDocumentEnriched;
  activeDocument?: IDocumentEnriched;
  isLoading: boolean;
  error: any;
  activePageNo?: number;
  pageImagesMap?: Record<string, Record<string, { imageUrl?: string; thumbUrl?: string }>>;
  documentJSON?: PageTokens[];
  pageIndexMap?: any;
  isDeletingMutation: boolean;
  currentImage?: string;
  isMutation: boolean;
  mutations: IDocumentEnriched[];
  createMutation: (partsToCopy?: string[]) => Promise<AxiosResponse>;
  setActivePage: (page: number) => void;
  addEntity: (entityData: IDocumentEntity) => Promise<void>;
  editEntity: (details: ActiveEntityPair, entityData: Partial<IDocumentEntity>) => Promise<void>;
  deleteEntity: (details: ActiveEntityPair) => Promise<void>;
  deleteDoc: () => Promise<void>;
  patchDoc: (payload: Record<string, any>) => Promise<AxiosResponse>;
  approveDoc: () => Promise<AxiosResponse>;
  resetTopology: () => Promise<any>;
  deleteTopologyPart: (partId: string) => Promise<void>;
  importMasterdataResultStatus: 'idle' | 'importing' | 'error';
  importMasterdataResult: (result: IClientMasterdataSearchResult) => Promise<void>;
  bounceDoc: (value: Record<string, any>) => Promise<void>;
  changeDocType: (
    docTypeId: string,
    subtypeId?: string,
    partId?: string,
    changeAll?: boolean,
  ) => Promise<any[]>;
  changeInbox: (newInboxId: string) => Promise<void>;
  handleNavNext: () => void;
  handleNavPrev: () => void;
  nextSiblingId?: string;
  prevSiblingId?: string;
  getDocumentPDF: (topologyId?: string) => Promise<any>;
  allLoading: boolean;
  setSelectedMutationId: (mutationId: string) => void;
  isJSONLoading: boolean;
  isMainDocLoading: boolean;
  isInteractive: boolean;
  //
  setIsViewerLoaded: (value: boolean) => void;
  isViewerLoaded: boolean;
};

const DocumentContext = createContext<DocumentContextType | null>(null);

export const DocumentProvider = ({
  children,
  mainDocId,
  inboxId,
  historical,
}: {
  children: React.ReactNode;
  mainDocId: string;
  inboxId: string;
  historical: boolean;
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // --------------------------------------------------------------------------
  // Local State
  // --------------------------------------------------------------------------
  const prevDocId = usePrevious(mainDocId);
  const tenantId = useSelector((state) => state.tenant.tenantId);
  const [lockedDocId, setLockedDocId] = useState(null);

  const [importMasterdataResultStatus, setImportMasterdataResultStatus] = useState<any>('idle');
  const [activePageNo, setActivePage] = useState(1);
  const [isViewerLoaded, setIsViewerLoaded] = useState(false);
  const [selectedMutationId, setSelectedMutationId] = useState('original');
  const userAccount = useSelector((state) => state.user.userAccount);
  const filters = useSelector((state) => state.documentList.filters);
  const pageImagesMap = useSelector((state) => state.documentList.pageImagesMap);
  const [isDeletingMutation, setIsDeletingMutation] = useState<boolean>(false);
  const [deletingMutationId, setDeletingMutationId] = useState<string | null>(null);

  const isMutation = selectedMutationId !== 'original';

  // --------------------------------------------------------------------------
  // RTK Query Data
  // --------------------------------------------------------------------------
  const {
    data: docDataR,
    isFetching: mainDocLoading,
    error,
  } = useGetDocumentQuery({ docId: mainDocId }, { skip: !mainDocId });

  const docData = useMemo(() => {
    if (mainDocLoading) return null;
    return docDataR;
  }, [docDataR, mainDocLoading]);

  const { data: nextSiblingId } = useGetNextSiblingQuery(
    { inboxId, filters, startAfterDocument: docData },
    { skip: historical || !docData || !filters },
  );
  const { data: prevSiblingId } = useGetPrevSiblingQuery(
    { inboxId, filters, endBeforeDocument: docData },
    { skip: historical || !docData },
  );
  const { data: historicalSiblings } = useGetHistoricalSiblingsQuery(
    { inboxId, documentId: mainDocId, filters },
    { skip: !historical || !mainDocId },
  );

  const { all: doctypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const { data: fieldTypes } = useGetFieldtypesQuery({ inboxId });
  const { data: tagTypes } = useGetTagtypesQuery({ inboxId });
  const { data: documentJSONR, isFetching: jsonLoading } = useGetDocumentJSONQuery({
    inboxId,
    docId: mainDocId,
  });
  const documentJSON = useMemo(() => {
    if (jsonLoading) return null;
    return documentJSONR;
  }, [documentJSONR, jsonLoading]);

  // --------------------------------------------------------------------------
  // Data reset
  // --------------------------------------------------------------------------

  const { data: mutations = [], isLoading: mutationsLoading } = useGetDocumentMutationsQuery(
    { docId: mainDocId },
    { skip: !mainDocId },
  );

  // --------------------------------------------------------------------------
  // Derived State
  // --------------------------------------------------------------------------

  const allLoading = jsonLoading || mutationsLoading || mainDocLoading;

  const mainDocument = useMemo(() => {
    if (!docData || !doctypes || !fieldTypes || !tagTypes) return null;
    const docCopy = { ...docData };
    if (docCopy.entities) docCopy.entities = sortEntities(docCopy, docCopy.entities);
    const doc = enrichDocumentWithTypes(docCopy, doctypes, fieldTypes, tagTypes);
    return doc;
  }, [docData, doctypes, fieldTypes, tagTypes]);

  const selectedMutation = useMemo(() => {
    if (!selectedMutationId || !mutations) return null;
    const selectedMutation = mutations.find((m) => m.id === selectedMutationId);
    if (!selectedMutation) return null;
    const mutationCopy = { ...selectedMutation };
    if (mutationCopy.entities) mutationCopy.entities = sortEntities(mutationCopy, mutationCopy.entities);

    return enrichDocumentWithTypes(mutationCopy, doctypes ?? [], fieldTypes ?? [], tagTypes ?? []);
  }, [mutations, selectedMutationId]);

  const activeDocument = useMemo(() => {
    // Your mutation selection logic here
    return selectedMutationId === 'original' ? mainDocument : selectedMutation;
  }, [selectedMutation, mainDocument, selectedMutationId]);

  const isLoading = useMemo(() => {
    return selectedMutationId && selectedMutationId !== 'original' ? mutationsLoading : mainDocLoading;
  }, [selectedMutationId, mutationsLoading, mainDocLoading]);

  const effectiveNextSiblingId = useMemo(() => {
    if (historical) {
      return historicalSiblings?.next || null;
    }
    return nextSiblingId;
  }, [historical, historicalSiblings, nextSiblingId]);

  const effectivePrevSiblingId = useMemo(() => {
    if (historical) {
      return historicalSiblings?.previous || null;
    }
    return prevSiblingId;
  }, [historical, historicalSiblings, prevSiblingId]);

  const isInteractive = useMemo(() => {
    // Return false if there's any entity with isPending status
    // if (activeDocument.entities?.some((e) => e.isPending)) {
    //   return false;
    // }
    if (activeDocument?.action) return false;
    console.log(lockedDocId);
    console.log(mainDocId);
    if (lockedDocId !== mainDocId) return false;
    // if (userAccount.isHidden) {
    //   if (!isPatching && !isProcessing && document != null) {
    //     return document?.nMutations > 0 || true;
    //   }
    // } else {
    //   if (!isPatching && !isProcessing && lockedId && document != null) {
    //     if (document.parentDocId) {
    //       return lockedId === document.parentDocId || lockedId === document.id;
    //     }
    //     return document?.nMutations > 0 || true;
    //   }
    // }

    return true;
  }, [lockedDocId, mainDocId, activeDocument]);

  // useConsole(isViewerLoaded, 'isViewerLoaded');
  // useConsole(activePageImage, 'activePageImage');

  const pageIndexMap = useMemo(() => {
    const map = [];
    if (activeDocument?.topology) {
      activeDocument.topology.parts.forEach((part) => {
        if (!part.archived) {
          map.push(...part.pages.filter((p) => !p.archived).map((e) => e.bundlePageNo));
        }
      });
    }
    return map;
  }, [activeDocument]);

  const isFinalDocWithoutAction = useMemo(() => {
    if (!mainDocument) return;
    if (selectedMutationId === 'original') {
      if (!mutations || mutations.length === 0) return true;
      const mutationsWithoutAction = mutations?.find((m) => !m.action);
      console.log(mutationsWithoutAction);
      return mutationsWithoutAction == null;
    }
    const otherMutationsWithoutAction = mutations?.find((m) => m.id !== selectedMutationId && !m.action);
    const mainDocWithoutAction = !!mainDocument.action;
    return !!otherMutationsWithoutAction && mainDocWithoutAction;
  }, [selectedMutationId, mainDocument, mutations]);
  useConsole(isFinalDocWithoutAction, 'isFinalDocWithoutAction');

  // --------------------------------------------------------------------------

  // --------------------------------------------------------------------------
  // Action Handlers (Memoized)
  // --------------------------------------------------------------------------

  const getDocumentPDF = (topologyId?: string) => {
    const mutationId = selectedMutationId !== 'original' ? selectedMutationId : undefined;
    return getRawPDF(inboxId, mainDocId, mutationId, topologyId);
  };

  const setActivePageWithReset = useCallback(
    (pageNo: number) => {
      if (!pageNo || pageIndexMap.length === 0) return;
      const boundary = pageIndexMap[pageIndexMap.length - 1];
      if (!boundary) return;
      const boundPageNo = Math.max(0, Math.min(boundary, pageNo));
      if (boundPageNo !== activePageNo) {
        setIsViewerLoaded(false);
        setActivePage(boundPageNo);
      }
    },
    [activePageNo, pageIndexMap, dispatch],
  );
  const handleNavNext = () => {
    if (effectiveNextSiblingId) {
      let path = `/inbox/${inboxId}/documents/${effectiveNextSiblingId}`;
      if (historical) path = `/inbox/${inboxId}/historical/${effectiveNextSiblingId}`;
      navigate(path);
    } else {
      navigate(`/inbox/${inboxId}`);
    }
  };
  const handleNavPrev = () => {
    if (effectivePrevSiblingId) {
      let path = `/inbox/${inboxId}/documents/${effectivePrevSiblingId}`;
      if (historical) path = `/inbox/${inboxId}/historical/${effectivePrevSiblingId}`;
      navigate(path);
    } else {
      navigate(`/inbox/${inboxId}`);
    }
  };

  const bounceDoc = async (value: Record<string, any>) => {
    const userEmail = userAccount?.email;
    await bounceDocument(
      value,
      userEmail,
      inboxId,
      mainDocId,
      selectedMutationId !== 'original' ? selectedMutationId : undefined,
    );
  };

  const resetTopology = async () => {
    if (!activeDocument?.topology?.parts) return;
    try {
      // Unarchive every part and its pages.
      const unarchivePart = (part: any) => ({
        ...part,
        archived: false,
        pages: part.pages.map((page: any) => ({ ...page, archived: false })),
      });
      const unarchivedParts = activeDocument.topology.parts.map(unarchivePart);
      const uniqueParts = unarchivedParts.filter(
        (part, index, parts) => parts.findIndex((p) => p.contentId === part.contentId) === index,
      );
      const partsMap: Record<string, any> = {};
      uniqueParts.forEach((part) => {
        const { id, ...rest } = part;
        partsMap[id] = { ...rest };
      });
      Object.keys(partsMap).forEach((key) => {
        partsMap[key].id = key;
      });
      const topology = {
        parts: Object.values(partsMap).reduce(
          (acc, currPart) => {
            acc[currPart.id] = convertPartToRaw(currPart);
            return acc;
          },
          {} as Record<string, any>,
        ),
      };
      return patchDocument(
        inboxId,
        { topology },
        mainDocId,
        selectedMutationId !== 'original' ? selectedMutationId : undefined,
      );
    } catch (error) {
      console.error('Error during undo transform:', error);
    }
  };

  const approveDoc = () => {
    if (!mainDocId || !activeDocument) return;
    const mutationId = selectedMutationId !== 'original' ? selectedMutationId : undefined;
    console.log(isFinalDocWithoutAction);
    // return;
    return patchDocument(
      inboxId,
      {
        action: { actor_email: userAccount.email, type: 'approve' },
      },
      mainDocId,
      mutationId,
    ).then((res) => {
      if (res?.request?.status === 204) {
        console.log(isFinalDocWithoutAction);
        if (isFinalDocWithoutAction) {
          handleNavNext();
        } else {
          const mutationWithoutAction = mutations.find((m) => !m.action && m.id !== selectedMutationId);
          console.log(mutationWithoutAction);
          if (mutationWithoutAction) {
            setSelectedMutationId(mutationWithoutAction.id);
          } else {
            setSelectedMutationId('original');
          }
        }
      }
      return res;
    });
  };

  const createMutation = async (partsToCopy: string[]): Promise<AxiosResponse> => {
    const documentId = mainDocument.id;
    return await createDocumentMutation(inboxId, documentId).then((res) => {
      if (!res.data.id) return;
      if (res.data.id) {
        if (!partsToCopy) {
          setSelectedMutationId(res.data.id);
        }
        const partsMap: Record<string, IDocumentTopologyPartEnriched> = {};
        activeDocument.topology.parts.forEach((p) => {
          partsMap[p.id] = { ...p, archived: !partsToCopy?.includes(p.id) };
          delete partsMap[p.id].id;
        });
        // Reassign IDs: remove the old id property and reassign the key as the id
        Object.keys(partsMap).forEach((key) => {
          const { id, ...rest } = partsMap[key];
          partsMap[key] = { ...rest, id: key };
        });
        // Build the new topology object by converting each part to raw format
        const topology = {
          parts: Object.values(partsMap).reduce(
            (acc, currPart) => {
              acc[currPart.id] = convertPartToRaw(currPart);
              return acc;
            },
            {} as Record<string, any>,
          ),
        };

        return patchDocument(inboxId, { topology }, mainDocId, res.data.id).then(() => {
          setSelectedMutationId(res.data.id);
          return res;
        });
      }
    });
  };

  const patchDoc = async (payload: Record<string, any>) => {
    if (!mainDocId || !activeDocument) return;
    const mutationId = selectedMutationId !== 'original' ? selectedMutationId : undefined;
    return patchDocument(inboxId, payload, mainDocId, mutationId);
  };

  //deletes the current document
  const deleteDoc = async () => {
    if (!mainDocId || !activeDocument) return;
    try {
      if (isMutation) {
        setIsDeletingMutation(true);
        setDeletingMutationId(selectedMutationId);
        await deleteMutation(inboxId, mainDocId, selectedMutationId).then(() => {
          const mutationWithoutAction = mutations.find((m) => !m.action);
          if (mutationWithoutAction) {
            setSelectedMutationId(mutationWithoutAction.id);
          } else {
            setSelectedMutationId('original');
          }
        });
      } else {
        await deleteDocument(inboxId, mainDocId, userAccount.email);
      }
    } catch (err) {
      setIsDeletingMutation(false);
      setDeletingMutationId(null);
      console.error('Failed to remove document:', err);
    }
  };

  const deleteTopologyPart = async (partId: string) => {
    if (!mainDocId || !activeDocument) return;
    try {
      const mutationId = selectedMutationId !== 'original' ? selectedMutationId : undefined;
      await patchTopologyPart({
        inboxId,
        partId,
        documentId: mainDocId,
        mutationId,
        payload: { archived: true },
      });
    } catch (err) {
      console.error('Failed to delete topology part:', err);
    }
  };

  const importMasterdataResult = async (result: IClientMasterdataSearchResult) => {
    if (!mainDocId || !activeDocument) return;
    setImportMasterdataResultStatus('importing');
    try {
      const mutationId = selectedMutationId !== 'original' ? selectedMutationId : undefined;

      return importMasterDataResult(inboxId, result, mainDocId, mutationId).then(() => {
        setImportMasterdataResultStatus('idle');
      });
    } catch (err) {
      setImportMasterdataResultStatus('error');
      console.error('Failed to import masterdata result:', err);
    }
  };

  const changeDocType = async (
    docTypeId: string,
    subtypeId?: string,
    partId?: string,
    changeAll?: boolean,
  ) => {
    if (!mainDocId || !activeDocument) return;
    const docsToChange = changeAll ? [mainDocId, ...mutations.map((m) => m.id)] : [mainDocId];
    try {
      return await Promise.all(
        docsToChange.map((docId) =>
          changeDocumentDoctype(
            inboxId,
            mainDocId,
            docTypeId,
            subtypeId,
            docId === mainDocId ? null : docId,
            partId,
          ),
        ),
      );
    } catch (err) {
      console.error('Failed to change document type:', err);
    }
  };

  const changeInbox = async (newInboxId: string): Promise<void> => {
    return changeDocumentInbox(mainDocId, inboxId, newInboxId);
  };

  const addEntity = async (entityData: IDocumentEntity) => {
    if (!mainDocId || !activeDocument) return;

    const newEntity: IDocumentEntity = {
      ...entityData,
    };
    const id = newEntity.id ?? uuid4hex();
    const isMutationActive = selectedMutationId !== 'original';

    // Create the entity
    const normalizedEntity = normalizeEntity(newEntity, mainDocument);
    const entityPayload = entityToRawPayload(normalizedEntity);

    // Add to pending operations
    dispatch(
      addPendingOperation({
        type: 'add',
        entityId: id,
        timestamp: Date.now(),
        data: normalizedEntity,
      }),
    );

    // Optimistic update
    if (isMutationActive) {
      dispatch(
        firestoreApi.util.updateQueryData(
          'getDocumentMutations',
          { docId: mainDocId },
          (draft: IDocument[]) => {
            const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
            if (mutationIndex !== -1) {
              if (!draft[mutationIndex].entities) draft[mutationIndex].entities = [];
              draft[mutationIndex].entities.push(normalizedEntity);
            }
          },
        ),
      );
    } else {
      dispatch(
        firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: IDocument) => {
          if (!draft.entities) draft.entities = [];
          draft.entities.push(normalizedEntity);
        }),
      );
    }

    try {
      const entityPostPayload: IDocumentEntityPostPayload = {
        pageNo: entityPayload.pageNo,
        rawValue: entityPayload.rawValue ?? null,
        value: entityPayload.value,
        type: entityPayload.type,
        valueLocations: entityPayload.valueLocations,
      };
      await addEntityToDocument(
        inboxId,
        mainDocId,
        id,
        entityPostPayload,
        isMutationActive ? selectedMutationId : undefined,
      );
    } catch (err) {
      // Rollback on error
      if (isMutationActive) {
        dispatch(
          firestoreApi.util.updateQueryData(
            'getDocumentMutations',
            { docId: mainDocId },
            (draft: IDocument[]) => {
              const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
              if (mutationIndex !== -1) {
                draft[mutationIndex].entities = draft[mutationIndex].entities.filter((e) => e.id !== id);
              }
            },
          ),
        );
      } else {
        dispatch(
          firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: IDocument) => {
            draft.entities = draft.entities.filter((e) => e.id !== id);
          }),
        );
      }

      dispatch(removePendingOperation({ entityId: id }));
      console.error('Failed to add entity:', err);
    }
  };

  const editEntity = async (details: ActiveEntityPair, entityData: Partial<IDocumentEntity>) => {
    if (!mainDocId || !activeDocument) return;

    const { entityId, childId } = details;
    const existing = activeDocument.entities.find((e) => e.id === entityId);
    if (!existing) {
      console.error('Entity not found:', entityId);
      return;
    }

    const isMutationActive = selectedMutationId !== 'original';

    // Rest of entity preparation logic...
    let updatedEntity;
    if (childId) {
      const complexValue = cloneDeep(existing.value as IDocumentEntityComplexValue);
      if (complexValue) {
        complexValue.complex[childId] = { ...complexValue.complex[childId], ...entityData };
        updatedEntity = { ...existing, value: complexValue };
      }
    } else {
      updatedEntity = { ...existing, ...entityData };
    }

    const normalizedEntity = normalizeEntity(updatedEntity, mainDocument);
    const entityPayload = entityToRawPayload(normalizedEntity);

    // Add to pending operations
    dispatch(
      addPendingOperation({
        type: 'edit',
        entityId,
        childId,
        timestamp: Date.now(),
        data: entityPayload,
        originalEntity: cloneDeep(existing),
      }),
    );

    // Optimistic update
    if (isMutationActive) {
      dispatch(
        firestoreApi.util.updateQueryData(
          'getDocumentMutations',
          { docId: mainDocId },
          (draft: IDocument[]) => {
            const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
            if (mutationIndex !== -1) {
              if (!draft[mutationIndex].entities) draft[mutationIndex].entities = [];
              const existingIndex = draft[mutationIndex].entities.findIndex((e) => e.id === entityId);
              if (existingIndex !== -1) {
                draft[mutationIndex].entities[existingIndex] = entityPayload;
              }
            }
          },
        ),
      );
    } else {
      // Original optimistic update for the main document
      dispatch(
        firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: IDocument) => {
          if (!draft.entities) draft.entities = [];
          const existingIndex = draft.entities.findIndex((e) => e.id === entityId);
          if (existingIndex !== -1) {
            draft.entities[existingIndex] = entityPayload;
          }
        }),
      );
    }

    // Create properly typed patch payload
    const entityDataToSend: IDocumentEntityPatchPayload = childId
      ? { value: { complex: entityPayload.value.complex } }
      : Object.keys(entityData).reduce((acc, key) => {
          acc[key] = entityPayload[key];
          return acc;
        }, {} as IDocumentEntityPatchPayload);

    try {
      await editEntityInDocument(
        inboxId,
        mainDocId,
        entityId,
        entityDataToSend,
        isMutationActive ? selectedMutationId : undefined,
      );
    } catch (err) {
      // Rollback on error
      if (isMutationActive) {
        dispatch(
          firestoreApi.util.updateQueryData(
            'getDocumentMutations',
            { docId: mainDocId },
            (draft: IDocument[]) => {
              const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
              if (mutationIndex !== -1) {
                const existingIndex = draft[mutationIndex].entities.findIndex((e) => e.id === entityId);
                if (existingIndex !== -1) {
                  if (childId) {
                    draft[mutationIndex].entities[existingIndex].value = cloneDeep(existing.value);
                  } else {
                    draft[mutationIndex].entities[existingIndex] = cloneDeep(existing);
                  }
                }
              }
            },
          ),
        );
      } else {
        // Original rollback logic
        dispatch(
          firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: IDocument) => {
            const existingIndex = draft.entities.findIndex((e) => e.id === entityId);
            if (existingIndex !== -1) {
              if (childId) {
                draft.entities[existingIndex].value = cloneDeep(existing.value);
              } else {
                draft.entities[existingIndex] = cloneDeep(existing);
              }
            }
          }),
        );
      }

      dispatch(removePendingOperation({ entityId, childId }));
      console.error('Failed to edit entity:', err);
    }
  };

  // Clean up the deleteEntity function:
  const deleteEntity = async (details: ActiveEntityPair) => {
    if (!mainDocId || !activeDocument) return;

    const { entityId, childId } = details;
    const existing = activeDocument.entities.find((e) => e.id === entityId);
    if (!existing) return;

    const isMutationActive = selectedMutationId !== 'original';

    // Rest of entity preparation logic...
    let updatedEntity;
    if (childId) {
      // For complex field - clear the child value but keep the field
      const complexValue = cloneDeep(existing.value as IDocumentEntityComplexValue);
      if (complexValue) {
        const child = complexValue.complex[childId];
        const emptyDefaultEntity = {
          id: childId,
          pageNo: null,
          rawValue: null,
          value: null,
          confidence: 0,
          type: child.type,
          valueLocations: [],
          source: 'user',
        };
        complexValue.complex[childId] = emptyDefaultEntity;
        updatedEntity = { ...existing, value: complexValue };
      }
    } else {
      updatedEntity = existing;
    }

    const normalizedEntity = normalizeEntity(updatedEntity, mainDocument);
    const entityPayload = entityToRawPayload(normalizedEntity);

    // Add to pending operations
    dispatch(
      addPendingOperation({
        type: 'delete',
        entityId,
        childId,
        data: entityPayload,
        timestamp: Date.now(),
        originalEntity: cloneDeep(existing),
      }),
    );

    try {
      if (childId) {
        await editEntityInDocument(
          inboxId,
          mainDocId,
          entityId,
          { value: entityPayload.value },
          isMutationActive ? selectedMutationId : undefined,
        );
      } else {
        await removeEntityFromDocument(
          inboxId,
          mainDocId,
          entityId,
          isMutationActive ? selectedMutationId : undefined,
        );
      }
    } catch (err) {
      // Rollback on error
      dispatch(removePendingOperation({ entityId, childId }));
      console.error('Failed to delete entity:', err);
    }
  };

  // --------------------------------------------------------------------------
  // Side Effects
  // --------------------------------------------------------------------------
  useEffect(() => {
    if (userAccount.isHidden) return;

    if (mainDocId && userAccount.email) {
      if (historical) {
        // For historical documents, ensure any existing locks are cleared
        clearHistoricalDocumentLock(tenantId, inboxId, mainDocId);
      } else {
        // Only lock non-historical documents
        lockDocument(tenantId, inboxId, mainDocId, userAccount.email, historical).then(() => {
          console.log('LOCKING DOC', mainDocId);
          setLockedDocId(mainDocId);
        });
      }
    }

    return () => {
      if (mainDocId && userAccount.email && !historical) {
        unlockDocument(tenantId, inboxId, mainDocId).then(() => {
          console.log('UNLOCKING DOC');
        });
      }
    };
  }, [tenantId, mainDocId, inboxId, userAccount, historical]);

  useEffect(() => {
    // Reset the selected mutation when the main document ID changes.
    if (mainDocId !== prevDocId) {
      setActivePageWithReset(1);
      setSelectedMutationId('original');
    }
  }, [mainDocId, prevDocId, dispatch]);

  useEffect(() => {
    if (isDeletingMutation && deletingMutationId) {
      const stillExists = mutations.some((m) => m.id === deletingMutationId);
      if (!stillExists) {
        const previousMutation = mutations[mutations.length - 1];
        const newSelected = previousMutation ? previousMutation.id : 'original';
        setSelectedMutationId(newSelected);
        setIsDeletingMutation(false);
        setDeletingMutationId(null);
      }
    }
  }, [mutations, isDeletingMutation, deletingMutationId, dispatch]);

  useEffect(() => {
    const current = pageImagesMap[mainDocId];
    if (current) {
      const list = Object.values(current);
      if (list[0]?.thumbUrl) dispatch(setIsThumbsVisible(true));
    } else {
      dispatch(setIsThumbsVisible(false));
    }
  }, [dispatch, mainDocId, pageImagesMap]);

  useEffect(() => {
    if (activeDocument) {
      document.title = `${activeDocument.name} | Paperbox`;
    }
    return () => {
      document.title = 'Home | Paperbox';
    };
  }, [activeDocument]);
  // --------------------------------------------------------------------------
  // Combine Context Value
  // --------------------------------------------------------------------------
  const contextValue = useMemo(
    () => ({
      // State
      mainDocument,
      getDocumentPDF,
      activeDocument,
      isLoading,
      error,
      activePageNo,
      documentJSON,
      pageIndexMap,
      isDeletingMutation,
      isMutation,
      mutations,
      setActivePage: setActivePageWithReset,
      addEntity,
      editEntity,
      deleteEntity,
      deleteDoc,
      createMutation,
      patchDoc,
      bounceDoc,
      changeDocType,
      deleteTopologyPart,
      changeInbox,
      handleNavPrev,
      handleNavNext,
      prevSiblingId: effectivePrevSiblingId,
      nextSiblingId: effectiveNextSiblingId,
      allLoading,
      setSelectedMutationId,
      resetTopology,
      isJSONLoading: jsonLoading,
      isMainDocLoading: mainDocLoading,
      setIsViewerLoaded,
      isInteractive,
      isViewerLoaded,
      importMasterdataResult,
      approveDoc,
      importMasterdataResultStatus,
    }),
    [
      approveDoc,
      getDocumentPDF,
      isInteractive,
      mainDocument,
      activeDocument,
      resetTopology,
      isLoading,
      error,
      activePageNo,
      documentJSON,
      pageIndexMap,
      createMutation,
      isDeletingMutation,
      isMutation,
      mutations,
      setActivePageWithReset,
      addEntity,
      editEntity,
      deleteEntity,
      deleteDoc,
      patchDoc,
      bounceDoc,
      changeDocType,
      deleteTopologyPart,
      changeInbox,
      handleNavPrev,
      handleNavNext,
      effectivePrevSiblingId,
      effectiveNextSiblingId,
      allLoading,
      setSelectedMutationId,
      jsonLoading,
      mainDocLoading,
      setIsViewerLoaded,
      isViewerLoaded,
      importMasterdataResult,
      importMasterdataResultStatus,
    ],
  );

  return <DocumentContext.Provider value={contextValue}>{children}</DocumentContext.Provider>;
};

// ----------------------------------------------------------------------------
// Custom Hook for Consumption
// ----------------------------------------------------------------------------
export const useDocumentContext = () => {
  const context = useContext(DocumentContext);
  if (!context) {
    throw new Error('useDocumentContext must be used within a DocumentProvider');
  }
  return context;
};
